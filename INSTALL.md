# ChronoTranslate 安装和使用指南

## 安装步骤

### 1. 准备API密钥

在使用插件之前，您需要获取以下API密钥中的至少一个：

#### Google APIs (推荐)
- **Google Translate API**: [获取密钥](https://cloud.google.com/translate/docs/setup)
- **Google Text-to-Speech API**: [获取密钥](https://cloud.google.com/text-to-speech/docs/quickstart)
- **Google Speech-to-Text API**: [获取密钥](https://cloud.google.com/speech-to-text/docs/quickstart)

#### OpenAI API (备选)
- **OpenAI API**: [获取密钥](https://platform.openai.com/api-keys)
  - 支持翻译、语音识别和语音合成

### 2. 安装Chrome插件

1. 打开Chrome浏览器
2. 进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹

### 3. 配置API密钥

1. 点击Chrome工具栏中的ChronoTranslate图标
2. 点击"打开设置"
3. 在设置页面中输入您的API密钥
4. 选择默认目标语言和语音偏好
5. 点击"保存设置"
6. 可选：点击"测试连接"验证API密钥是否正确

## 使用方法

### 基本使用

1. 打开YouTube视频页面
2. 插件会自动在视频播放器下方显示控制面板
3. 选择目标翻译语言
4. 点击"开始翻译"按钮

### 两种翻译模式

#### 模式A：字幕翻译（推荐）
- **适用场景**：视频有字幕的情况
- **优点**：延迟低（1-2秒）、准确度高、成本低
- **工作原理**：直接翻译视频字幕，无需语音识别

#### 模式B：音频翻译
- **适用场景**：视频没有字幕的情况
- **特点**：延迟较高（3-5秒）、需要更多API调用
- **工作原理**：捕获音频 → 语音识别 → 翻译 → 语音合成

### 高级功能

- **同步缓冲调节**：在高级设置中调整音画同步的缓冲时间
- **实时统计**：查看已翻译句数、当前延迟等信息
- **缓存机制**：相同视频的翻译结果会被缓存，提高效率

## 故障排除

### 常见问题

1. **插件没有显示控制面板**
   - 确保在YouTube视频页面（包含 `/watch` 的URL）
   - 刷新页面重试
   - 检查浏览器控制台是否有错误信息

2. **翻译无法启动**
   - 检查API密钥是否正确配置
   - 在设置页面点击"测试连接"
   - 确保网络连接正常

3. **音画不同步**
   - 调整高级设置中的"同步缓冲"参数
   - 尝试暂停并重新播放视频

4. **音频模式无法工作**
   - 确保浏览器允许音频捕获权限
   - 检查是否有其他应用占用麦克风

### 性能优化建议

- 优先使用字幕模式（延迟更低）
- 定期清理浏览器缓存
- 避免同时打开多个翻译任务

## API成本估算

### Google APIs
- **Translate API**: $20/百万字符
- **Text-to-Speech**: $4/百万字符
- **Speech-to-Text**: $1.44/小时

### OpenAI API
- **GPT-3.5-turbo**: $0.002/1K tokens
- **Whisper**: $0.006/分钟
- **TTS**: $0.015/1K字符

### 成本节省建议
- 优先使用字幕模式
- 利用缓存机制避免重复翻译
- 合理选择目标语言（避免频繁切换）

## 技术支持

如果遇到问题，请：
1. 检查浏览器控制台的错误信息
2. 确认API密钥配置正确
3. 尝试重新加载插件
4. 查看GitHub Issues或提交新问题

## 隐私说明

- 所有API密钥仅存储在本地浏览器中
- 不会收集或上传任何个人数据
- 音频数据仅用于实时翻译，不会被保存
