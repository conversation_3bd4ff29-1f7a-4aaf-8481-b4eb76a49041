/**
 * ChronoTranslate Content Script V2.0
 * 重构版本 - 面向对象设计，支持多平台扩展
 */

import { ChronoTranslate } from './core/ChronoTranslate.js';
import { UIManager } from './ui/UIManager.js';
import { Logger } from './core/Logger.js';

class ChronoTranslateApp {
  constructor() {
    this.chronoTranslate = null;
    this.uiManager = null;
    this.logger = null;
    this.initialized = false;
    
    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      // 等待页面加载完成
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // 初始化日志器
      this.logger = new Logger(true);
      this.logger.info('ChronoTranslate App initializing...');

      // 初始化核心翻译引擎
      this.chronoTranslate = new ChronoTranslate({
        targetLanguage: 'zh-CN',
        bufferSize: 30,
        syncBuffer: 0.5,
        enableCache: true,
        enableDebug: true
      });

      // 初始化UI管理器
      this.uiManager = new UIManager({
        chronoTranslate: this.chronoTranslate,
        logger: this.logger
      });

      // 设置事件监听
      this.setupEventListeners();

      // 等待核心组件初始化完成
      await this.chronoTranslate.init();
      await this.uiManager.init();

      this.initialized = true;
      this.logger.info('ChronoTranslate App initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize ChronoTranslate App:', error);
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听核心翻译引擎事件
    this.chronoTranslate.on('initialized', () => {
      this.logger.info('ChronoTranslate core initialized');
    });

    this.chronoTranslate.on('platformDetected', (platform) => {
      this.logger.info(`Platform detected: ${platform.name}`);
      this.uiManager.showPlatformInfo(platform);
    });

    this.chronoTranslate.on('translationStarted', () => {
      this.logger.info('Translation started');
      this.uiManager.updateTranslationState('started');
    });

    this.chronoTranslate.on('translationStopped', () => {
      this.logger.info('Translation stopped');
      this.uiManager.updateTranslationState('stopped');
    });

    this.chronoTranslate.on('statusUpdate', (status) => {
      this.uiManager.updateStatus(status.message, status.type);
    });

    this.chronoTranslate.on('error', (error) => {
      this.logger.error('ChronoTranslate error:', error);
      this.uiManager.showError(error.message);
    });

    // 监听UI事件
    this.uiManager.on('startTranslation', (options) => {
      this.startTranslation(options);
    });

    this.uiManager.on('stopTranslation', () => {
      this.stopTranslation();
    });

    this.uiManager.on('settingsChanged', (settings) => {
      this.updateSettings(settings);
    });

    // 监听页面导航变化
    this.setupNavigationListener();
  }

  /**
   * 设置页面导航监听
   */
  setupNavigationListener() {
    let lastUrl = location.href;
    
    const observer = new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        this.logger.info(`Navigation detected: ${url}`);
        
        // 页面导航时重新检测平台
        setTimeout(async () => {
          await this.chronoTranslate.detectCurrentPlatform();
        }, 1000);
      }
    });

    observer.observe(document, { subtree: true, childList: true });
  }

  /**
   * 开始翻译
   */
  async startTranslation(options = {}) {
    if (!this.initialized) {
      this.logger.warn('App not initialized yet');
      return;
    }

    try {
      await this.chronoTranslate.startTranslation(options);
    } catch (error) {
      this.logger.error('Failed to start translation:', error);
      this.uiManager.showError(`翻译启动失败: ${error.message}`);
    }
  }

  /**
   * 停止翻译
   */
  async stopTranslation() {
    if (!this.initialized) {
      return;
    }

    try {
      await this.chronoTranslate.stopTranslation();
    } catch (error) {
      this.logger.error('Failed to stop translation:', error);
    }
  }

  /**
   * 更新设置
   */
  updateSettings(settings) {
    if (!this.initialized) {
      return;
    }

    this.chronoTranslate.updateOptions(settings);
    this.logger.info('Settings updated:', settings);
  }

  /**
   * 获取应用状态
   */
  getState() {
    if (!this.initialized) {
      return { initialized: false };
    }

    return {
      initialized: true,
      chronoTranslate: this.chronoTranslate.getState(),
      ui: this.uiManager.getState()
    };
  }

  /**
   * 销毁应用
   */
  async destroy() {
    this.logger.info('Destroying ChronoTranslate App...');

    if (this.chronoTranslate) {
      await this.chronoTranslate.destroy();
    }

    if (this.uiManager) {
      await this.uiManager.destroy();
    }

    this.initialized = false;
    this.logger.info('ChronoTranslate App destroyed');
  }
}

// 全局实例
let chronoTranslateApp = null;

// 初始化应用
(async () => {
  try {
    chronoTranslateApp = new ChronoTranslateApp();
    
    // 暴露到全局作用域供调试使用
    if (typeof window !== 'undefined') {
      window.ChronoTranslateApp = chronoTranslateApp;
      window.ctApp = chronoTranslateApp; // 简短别名
    }
    
  } catch (error) {
    console.error('Failed to initialize ChronoTranslate:', error);
  }
})();

// 页面卸载时清理
window.addEventListener('beforeunload', async () => {
  if (chronoTranslateApp) {
    await chronoTranslateApp.destroy();
  }
});

// 导出供其他模块使用
export { ChronoTranslateApp };
