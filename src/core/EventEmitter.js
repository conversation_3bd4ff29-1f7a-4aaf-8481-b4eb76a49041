/**
 * 事件发射器基类
 * 提供事件监听和发射功能
 */

export class EventEmitter {
  constructor() {
    this.events = new Map();
  }

  /**
   * 添加事件监听器
   * @param {string} event 事件名称
   * @param {Function} listener 监听器函数
   * @param {Object} options 选项
   */
  on(event, listener, options = {}) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listenerObj = {
      listener,
      once: options.once || false,
      priority: options.priority || 0
    };

    const listeners = this.events.get(event);
    listeners.push(listenerObj);

    // 按优先级排序
    listeners.sort((a, b) => b.priority - a.priority);

    return this;
  }

  /**
   * 添加一次性事件监听器
   * @param {string} event 事件名称
   * @param {Function} listener 监听器函数
   */
  once(event, listener) {
    return this.on(event, listener, { once: true });
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {Function} listener 监听器函数
   */
  off(event, listener) {
    if (!this.events.has(event)) {
      return this;
    }

    const listeners = this.events.get(event);
    const index = listeners.findIndex(l => l.listener === listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }

    if (listeners.length === 0) {
      this.events.delete(event);
    }

    return this;
  }

  /**
   * 发射事件
   * @param {string} event 事件名称
   * @param {...any} args 参数
   */
  emit(event, ...args) {
    if (!this.events.has(event)) {
      return false;
    }

    const listeners = this.events.get(event);
    const toRemove = [];

    for (let i = 0; i < listeners.length; i++) {
      const listenerObj = listeners[i];
      
      try {
        listenerObj.listener.apply(this, args);
        
        if (listenerObj.once) {
          toRemove.push(i);
        }
      } catch (error) {
        console.error(`Error in event listener for '${event}':`, error);
      }
    }

    // 移除一次性监听器
    for (let i = toRemove.length - 1; i >= 0; i--) {
      listeners.splice(toRemove[i], 1);
    }

    return true;
  }

  /**
   * 移除所有监听器
   * @param {string} event 可选的事件名称
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
    return this;
  }

  /**
   * 获取事件监听器数量
   * @param {string} event 事件名称
   */
  listenerCount(event) {
    if (!this.events.has(event)) {
      return 0;
    }
    return this.events.get(event).length;
  }

  /**
   * 获取所有事件名称
   */
  eventNames() {
    return Array.from(this.events.keys());
  }
}
