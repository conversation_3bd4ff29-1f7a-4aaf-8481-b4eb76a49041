/**
 * 翻译引擎
 * 支持多个翻译服务提供商和扩展
 */

import { EventEmitter } from './EventEmitter.js';
import { GoogleTranslateProvider } from '../providers/GoogleTranslateProvider.js';
import { OpenAITranslateProvider } from '../providers/OpenAITranslateProvider.js';
import { AzureTranslateProvider } from '../providers/AzureTranslateProvider.js';

export class TranslationEngine extends EventEmitter {
  constructor() {
    super();
    this.providers = new Map();
    this.currentProvider = null;
    this.fallbackProviders = [];
    this.apiKeys = {};
  }

  /**
   * 初始化翻译引擎
   */
  async init() {
    // 注册翻译服务提供商
    this.registerProvider('google', new GoogleTranslateProvider());
    this.registerProvider('openai', new OpenAITranslateProvider());
    this.registerProvider('azure', new AzureTranslateProvider());

    // 加载API密钥
    await this.loadApiKeys();

    // 设置默认提供商和降级链
    this.setupProviderChain();
  }

  /**
   * 注册翻译提供商
   * @param {string} name 提供商名称
   * @param {BaseTranslateProvider} provider 提供商实例
   */
  registerProvider(name, provider) {
    provider.name = name;
    this.providers.set(name, provider);
    
    // 监听提供商事件
    provider.on('translationComplete', (result) => {
      this.emit('translationComplete', { provider: name, ...result });
    });

    provider.on('error', (error) => {
      this.emit('providerError', { provider: name, error });
    });
  }

  /**
   * 加载API密钥
   */
  async loadApiKeys() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['apiKeys']);
        this.apiKeys = result.apiKeys || {};
      }
    } catch (error) {
      console.error('Error loading API keys:', error);
    }
  }

  /**
   * 更新API密钥
   * @param {Object} keys 新的API密钥
   */
  async updateApiKeys(keys) {
    this.apiKeys = { ...this.apiKeys, ...keys };
    
    // 更新提供商的API密钥
    for (const [name, provider] of this.providers) {
      if (provider.setApiKey) {
        const keyName = this.getApiKeyName(name);
        if (this.apiKeys[keyName]) {
          provider.setApiKey(this.apiKeys[keyName]);
        }
      }
    }

    // 重新设置提供商链
    this.setupProviderChain();

    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({ apiKeys: this.apiKeys });
    }
  }

  /**
   * 获取API密钥名称
   * @param {string} providerName 提供商名称
   */
  getApiKeyName(providerName) {
    const keyMap = {
      'google': 'googleTranslate',
      'openai': 'openai',
      'azure': 'azure'
    };
    return keyMap[providerName] || providerName;
  }

  /**
   * 设置提供商链
   */
  setupProviderChain() {
    const availableProviders = [];

    // 检查哪些提供商有可用的API密钥
    for (const [name, provider] of this.providers) {
      const keyName = this.getApiKeyName(name);
      if (this.apiKeys[keyName]) {
        provider.setApiKey(this.apiKeys[keyName]);
        availableProviders.push(name);
      }
    }

    // 设置主要提供商和降级链
    if (availableProviders.length > 0) {
      this.currentProvider = availableProviders[0];
      this.fallbackProviders = availableProviders.slice(1);
    }
  }

  /**
   * 批量翻译文本
   * @param {Array<string>} texts 要翻译的文本数组
   * @param {string} targetLanguage 目标语言
   * @param {Object} options 选项
   */
  async batchTranslate(texts, targetLanguage, options = {}) {
    if (!texts || texts.length === 0) {
      return [];
    }

    const providers = [this.currentProvider, ...this.fallbackProviders];
    
    for (const providerName of providers) {
      if (!providerName) continue;
      
      const provider = this.providers.get(providerName);
      if (!provider) continue;

      try {
        console.log(`Attempting translation with ${providerName}`);
        
        const results = await provider.batchTranslate(texts, targetLanguage, options);
        
        if (results && results.length === texts.length) {
          this.emit('translationComplete', {
            provider: providerName,
            texts,
            results,
            targetLanguage
          });
          
          return results;
        }
        
      } catch (error) {
        console.error(`Translation failed with ${providerName}:`, error);
        this.emit('providerError', { provider: providerName, error });
        
        // 如果不是最后一个提供商，继续尝试下一个
        if (providerName !== providers[providers.length - 1]) {
          console.log(`Falling back to next provider...`);
          continue;
        }
      }
    }

    throw new Error('All translation providers failed');
  }

  /**
   * 翻译单个文本
   * @param {string} text 要翻译的文本
   * @param {string} targetLanguage 目标语言
   * @param {Object} options 选项
   */
  async translateText(text, targetLanguage, options = {}) {
    const results = await this.batchTranslate([text], targetLanguage, options);
    return results[0] || text;
  }

  /**
   * 检测文本语言
   * @param {string} text 要检测的文本
   */
  async detectLanguage(text) {
    const providers = [this.currentProvider, ...this.fallbackProviders];
    
    for (const providerName of providers) {
      if (!providerName) continue;
      
      const provider = this.providers.get(providerName);
      if (!provider || !provider.detectLanguage) continue;

      try {
        const language = await provider.detectLanguage(text);
        if (language) {
          return language;
        }
      } catch (error) {
        console.error(`Language detection failed with ${providerName}:`, error);
      }
    }

    return 'auto';
  }

  /**
   * 获取支持的语言列表
   * @param {string} providerName 提供商名称（可选）
   */
  async getSupportedLanguages(providerName) {
    const provider = providerName ? 
      this.providers.get(providerName) : 
      this.providers.get(this.currentProvider);

    if (provider && provider.getSupportedLanguages) {
      return await provider.getSupportedLanguages();
    }

    // 返回通用语言列表
    return [
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'zh-TW', name: '中文 (繁體)' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' },
      { code: 'ko', name: '한국어' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'ru', name: 'Русский' }
    ];
  }

  /**
   * 获取当前提供商
   */
  getCurrentProvider() {
    return this.currentProvider;
  }

  /**
   * 设置当前提供商
   * @param {string} providerName 提供商名称
   */
  setCurrentProvider(providerName) {
    if (this.providers.has(providerName)) {
      this.currentProvider = providerName;
      
      // 重新排列降级链
      this.fallbackProviders = Array.from(this.providers.keys())
        .filter(name => name !== providerName);
    }
  }

  /**
   * 获取提供商状态
   */
  getProviderStatus() {
    const status = {};
    
    for (const [name, provider] of this.providers) {
      const keyName = this.getApiKeyName(name);
      status[name] = {
        hasApiKey: !!this.apiKeys[keyName],
        isAvailable: provider.isAvailable ? provider.isAvailable() : true,
        isCurrent: name === this.currentProvider
      };
    }
    
    return status;
  }

  /**
   * 测试提供商连接
   * @param {string} providerName 提供商名称
   */
  async testProvider(providerName) {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }

    const keyName = this.getApiKeyName(providerName);
    if (!this.apiKeys[keyName]) {
      throw new Error(`API key not configured for ${providerName}`);
    }

    try {
      // 测试翻译一个简单的文本
      const testText = 'Hello';
      const result = await provider.translateText(testText, 'zh-CN');
      
      if (result && result !== testText) {
        return { success: true, result };
      } else {
        throw new Error('Translation test failed');
      }
    } catch (error) {
      throw new Error(`Provider test failed: ${error.message}`);
    }
  }

  /**
   * 获取翻译统计信息
   */
  getStatistics() {
    const stats = {
      totalTranslations: 0,
      providerUsage: {},
      errors: 0
    };

    // 这里可以实现统计信息的收集和返回
    // 实际实现中可能需要持久化存储

    return stats;
  }

  /**
   * 销毁翻译引擎
   */
  async destroy() {
    // 清理所有提供商
    for (const provider of this.providers.values()) {
      if (provider.destroy) {
        await provider.destroy();
      }
    }

    this.providers.clear();
    this.currentProvider = null;
    this.fallbackProviders = [];
    this.removeAllListeners();
  }
}
