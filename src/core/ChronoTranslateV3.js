/**
 * ChronoTranslate V3.0 主入口
 * 基于新架构的简洁API门面
 */

import { EventEmitter } from './EventEmitter.js';
import { TranslationService } from '../services/TranslationService.js';
import { ConfigService } from '../services/ConfigService.js';
import { Logger } from './Logger.js';

export class ChronoTranslateV3 extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      debug: false,
      autoInit: true,
      ...options
    };

    // 初始化服务
    this.logger = new Logger(this.options.debug);
    this.translationService = new TranslationService();
    this.configService = new ConfigService();

    // 系统状态
    this.state = {
      initialized: false,
      version: '3.0.0'
    };

    // 绑定事件
    this.setupEventHandlers();

    // 自动初始化
    if (this.options.autoInit) {
      this.init().catch(error => {
        this.logger.error('Auto initialization failed:', error);
      });
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 翻译服务事件
    this.translationService.on('initialized', () => {
      this.emit('serviceReady');
    });

    this.translationService.on('translationStarted', (event) => {
      this.emit('translationStarted', event);
    });

    this.translationService.on('translationProgress', (event) => {
      this.emit('translationProgress', event);
    });

    this.translationService.on('translationCompleted', (event) => {
      this.emit('translationCompleted', event);
    });

    this.translationService.on('translationFailed', (event) => {
      this.emit('translationFailed', event);
    });

    this.translationService.on('ttsProgress', (event) => {
      this.emit('ttsProgress', event);
    });

    this.translationService.on('error', (error) => {
      this.logger.error('Translation service error:', error);
      this.emit('error', error);
    });
  }

  /**
   * 初始化系统
   * @param {Object} config 初始化配置
   * @returns {Promise<void>}
   */
  async init(config = {}) {
    if (this.state.initialized) {
      return;
    }

    this.logger.info('Initializing ChronoTranslate V3.0...');

    try {
      // 初始化翻译服务
      await this.translationService.initialize(config);

      this.state.initialized = true;
      this.logger.info('ChronoTranslate V3.0 initialized successfully');
      this.emit('initialized');

    } catch (error) {
      this.logger.error('Failed to initialize ChronoTranslate:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 翻译视频
   * @param {HTMLVideoElement|Array} videoOrSubtitles 视频元素或字幕数组
   * @param {Object} options 翻译选项
   * @returns {Promise<string>} 任务ID
   */
  async translateVideo(videoOrSubtitles, options = {}) {
    if (!this.state.initialized) {
      throw new Error('ChronoTranslate not initialized. Call init() first.');
    }

    this.logger.info('Starting video translation...');
    
    try {
      const taskId = await this.translationService.translateVideo(videoOrSubtitles, options);
      this.logger.info(`Translation started with task ID: ${taskId}`);
      return taskId;
    } catch (error) {
      this.logger.error('Failed to start translation:', error);
      throw error;
    }
  }

  /**
   * 停止翻译
   * @returns {Promise<void>}
   */
  async stopTranslation() {
    this.logger.info('Stopping translation...');
    await this.translationService.stopTranslation();
  }

  /**
   * 暂停翻译
   * @returns {Promise<void>}
   */
  async pauseTranslation() {
    this.logger.info('Pausing translation...');
    await this.translationService.pauseTranslation();
  }

  /**
   * 恢复翻译
   * @returns {Promise<void>}
   */
  async resumeTranslation() {
    this.logger.info('Resuming translation...');
    await this.translationService.resumeTranslation();
  }

  /**
   * 获取翻译进度
   * @param {string} taskId 任务ID（可选）
   * @returns {Object|null}
   */
  getProgress(taskId = null) {
    return this.translationService.getProgress(taskId);
  }

  /**
   * 获取翻译结果
   * @param {string} taskId 任务ID（可选）
   * @returns {Promise<Array|null>}
   */
  async getResult(taskId = null) {
    return await this.translationService.getResult(taskId);
  }

  /**
   * 更新配置
   * @param {Object} config 新配置
   * @returns {Promise<void>}
   */
  async updateConfig(config) {
    this.logger.info('Updating configuration...');
    await this.translationService.updateConfig(config);
    this.emit('configUpdated', config);
  }

  /**
   * 获取当前配置
   * @returns {Promise<Object>}
   */
  async getConfig() {
    return await this.configService.load();
  }

  /**
   * 获取系统状态
   * @returns {Object}
   */
  getStatus() {
    return {
      ...this.state,
      translationService: this.translationService.getStatus()
    };
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    return this.translationService.getStatistics();
  }

  /**
   * 销毁系统
   * @returns {Promise<void>}
   */
  async destroy() {
    this.logger.info('Destroying ChronoTranslate V3.0...');
    
    try {
      await this.translationService.destroy();
      this.state.initialized = false;
      this.removeAllListeners();
      
      this.logger.info('ChronoTranslate V3.0 destroyed successfully');
      
    } catch (error) {
      this.logger.error('Failed to destroy ChronoTranslate:', error);
      throw error;
    }
  }

  /**
   * 获取版本信息
   * @returns {Object}
   */
  getVersion() {
    return {
      version: this.state.version,
      architecture: 'Queue-based OOP',
      features: [
        'Pure processors',
        'Queue coordination',
        'Batch processing',
        'Event-driven',
        'Configurable'
      ]
    };
  }

  // ========== 便捷方法 ==========

  /**
   * 快速翻译当前页面视频
   * @param {Object} options 翻译选项
   * @returns {Promise<string>} 任务ID
   */
  async quickTranslate(options = {}) {
    // 查找页面中的视频元素
    const video = document.querySelector('video');
    if (!video) {
      throw new Error('No video element found on the page');
    }

    return await this.translateVideo(video, options);
  }

  /**
   * 设置API密钥
   * @param {Object} apiKeys API密钥对象
   * @returns {Promise<void>}
   */
  async setApiKeys(apiKeys) {
    const config = {};
    
    if (apiKeys.translation || apiKeys.google) {
      config.translationApiKey = apiKeys.translation || apiKeys.google;
    }
    
    if (apiKeys.tts || apiKeys.googleTTS) {
      config.ttsApiKey = apiKeys.tts || apiKeys.googleTTS;
    }

    await this.updateConfig(config);
  }

  /**
   * 设置目标语言
   * @param {string} language 语言代码
   * @returns {Promise<void>}
   */
  async setTargetLanguage(language) {
    await this.updateConfig({ targetLanguage: language });
  }

  /**
   * 启用/禁用TTS
   * @param {boolean} enabled 是否启用
   * @returns {Promise<void>}
   */
  async setTTSEnabled(enabled) {
    await this.updateConfig({ enableTTS: enabled });
  }

  /**
   * 检查系统是否就绪
   * @returns {boolean}
   */
  isReady() {
    return this.state.initialized && this.translationService.getStatus().initialized;
  }

  /**
   * 等待系统就绪
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise<void>}
   */
  async waitForReady(timeout = 10000) {
    return new Promise((resolve, reject) => {
      if (this.isReady()) {
        resolve();
        return;
      }

      const timer = setTimeout(() => {
        reject(new Error('Timeout waiting for system to be ready'));
      }, timeout);

      const checkReady = () => {
        if (this.isReady()) {
          clearTimeout(timer);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };

      checkReady();
    });
  }
}

// 创建全局实例
export const chronoTranslate = new ChronoTranslateV3();

// 兼容旧版本API
export { ChronoTranslateV3 as ChronoTranslate };
