/**
 * 平台管理器
 * 负责检测和管理不同的视频平台
 */

import { EventEmitter } from './EventEmitter.js';
import { YouTubePlatform } from '../platforms/YouTubePlatform.js';
import { TikTokPlatform } from '../platforms/TikTokPlatform.js';
import { BilibiliPlatform } from '../platforms/BilibiliPlatform.js';
import { GenericPlatform } from '../platforms/GenericPlatform.js';

export class PlatformManager extends EventEmitter {
  constructor() {
    super();
    this.platforms = new Map();
    this.currentPlatform = null;
    this.mediaSniffing = null;
  }

  /**
   * 初始化平台管理器
   */
  async init() {
    // 注册所有支持的平台
    this.registerPlatform('youtube', new YouTubePlatform());
    this.registerPlatform('tiktok', new TikTokPlatform());
    this.registerPlatform('bilibili', new BilibiliPlatform());
    this.registerPlatform('generic', new GenericPlatform());

    // 启动媒体嗅探
    this.startMediaSniffing();
  }

  /**
   * 注册平台
   * @param {string} name 平台名称
   * @param {BasePlatform} platform 平台实例
   */
  registerPlatform(name, platform) {
    platform.name = name;
    this.platforms.set(name, platform);
    
    // 监听平台事件
    platform.on('videoDetected', (videoInfo) => {
      this.emit('videoDetected', { platform: name, ...videoInfo });
    });

    platform.on('subtitlesFound', (subtitles) => {
      this.emit('subtitlesFound', { platform: name, subtitles });
    });
  }

  /**
   * 检测当前平台
   * @param {string} url 当前页面URL
   */
  async detectPlatform(url) {
    for (const [name, platform] of this.platforms) {
      if (await platform.isSupported(url)) {
        this.currentPlatform = platform;
        this.emit('platformDetected', platform);
        return platform;
      }
    }

    // 如果没有匹配的平台，使用通用平台
    const genericPlatform = this.platforms.get('generic');
    if (genericPlatform && await genericPlatform.isSupported(url)) {
      this.currentPlatform = genericPlatform;
      this.emit('platformDetected', genericPlatform);
      return genericPlatform;
    }

    return null;
  }

  /**
   * 获取当前平台
   */
  getCurrentPlatform() {
    return this.currentPlatform;
  }

  /**
   * 获取所有注册的平台
   */
  getAllPlatforms() {
    return Array.from(this.platforms.values());
  }

  /**
   * 根据名称获取平台
   * @param {string} name 平台名称
   */
  getPlatform(name) {
    return this.platforms.get(name);
  }

  /**
   * 启动媒体嗅探
   */
  startMediaSniffing() {
    if (typeof chrome !== 'undefined' && chrome.webRequest) {
      // 在background script中运行
      this.setupBackgroundMediaSniffing();
    } else {
      // 在content script中运行
      this.setupContentMediaSniffing();
    }
  }

  /**
   * 设置后台媒体嗅探
   */
  setupBackgroundMediaSniffing() {
    chrome.webRequest.onBeforeSendHeaders.addListener(
      (details) => {
        this.handleMediaRequest(details);
      },
      {
        urls: ["<all_urls>"],
        types: ["xmlhttprequest", "media", "other"]
      },
      ["requestHeaders"]
    );
  }

  /**
   * 设置内容脚本媒体嗅探
   */
  setupContentMediaSniffing() {
    // 监听页面中的媒体元素
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查视频元素
            if (node.tagName === 'VIDEO') {
              this.handleVideoElement(node);
            }
            
            // 检查子元素中的视频
            const videos = node.querySelectorAll && node.querySelectorAll('video');
            if (videos) {
              videos.forEach(video => this.handleVideoElement(video));
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // 检查已存在的视频元素
    document.querySelectorAll('video').forEach(video => {
      this.handleVideoElement(video);
    });
  }

  /**
   * 处理媒体请求
   * @param {Object} details 请求详情
   */
  handleMediaRequest(details) {
    const url = details.url;
    const tabId = details.tabId;
    
    if (this.isMediaUrl(url)) {
      console.log('ChronoTranslate: Media URL detected:', url);
      
      // 存储媒体信息
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.session.set({
          [`media_${tabId}`]: {
            videoUrl: url,
            tabId: tabId,
            timestamp: Date.now(),
            referrer: details.initiator || details.documentUrl
          }
        });
      }

      this.emit('mediaDetected', {
        url,
        tabId,
        referrer: details.initiator || details.documentUrl
      });
    }
  }

  /**
   * 处理视频元素
   * @param {HTMLVideoElement} video 视频元素
   */
  handleVideoElement(video) {
    if (video.dataset.chronoProcessed) {
      return;
    }

    video.dataset.chronoProcessed = 'true';

    // 监听视频事件
    video.addEventListener('loadedmetadata', () => {
      this.emit('videoDetected', {
        element: video,
        src: video.src || video.currentSrc,
        duration: video.duration
      });
    });

    video.addEventListener('play', () => {
      this.emit('videoPlay', { element: video });
    });

    video.addEventListener('pause', () => {
      this.emit('videoPause', { element: video });
    });
  }

  /**
   * 检查URL是否为媒体资源
   * @param {string} url URL地址
   */
  isMediaUrl(url) {
    const mediaExtensions = ['.mp4', '.m3u8', '.webm', '.mkv', '.avi', '.mov'];
    const mediaDomains = [
      'googlevideo.com',
      'youtube.com',
      'youtu.be',
      'tiktok.com',
      'douyin.com',
      'bilibili.com',
      'hdslb.com'
    ];
    
    // 检查文件扩展名
    if (mediaExtensions.some(ext => url.toLowerCase().includes(ext))) {
      return true;
    }
    
    // 检查特定域名的媒体URL模式
    if (mediaDomains.some(domain => url.includes(domain))) {
      // YouTube视频URL模式
      if (url.includes('googlevideo.com') && url.includes('mime=video')) {
        return true;
      }
      
      // TikTok视频URL模式
      if (url.includes('tiktok') && url.includes('video')) {
        return true;
      }
      
      // B站视频URL模式
      if (url.includes('bilibili.com') || url.includes('hdslb.com')) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 销毁平台管理器
   */
  async destroy() {
    // 清理所有平台
    for (const platform of this.platforms.values()) {
      if (platform.destroy) {
        await platform.destroy();
      }
    }

    this.platforms.clear();
    this.currentPlatform = null;
    this.removeAllListeners();
  }
}
