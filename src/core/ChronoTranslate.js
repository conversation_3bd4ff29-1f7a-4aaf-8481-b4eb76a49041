/**
 * ChronoTranslate 核心类
 * 面向对象设计，支持多平台扩展
 */

import { EventEmitter } from './EventEmitter.js';
import { PlatformManager } from './PlatformManager.js';
import { TranslationEngine } from './TranslationEngine.js';
import { AudioEngine } from './AudioEngine.js';
import { CacheManager } from './CacheManager.js';
import { MiddlewareManager } from './MiddlewareManager.js';
import { Logger } from './Logger.js';

export class ChronoTranslate extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      targetLanguage: 'zh-CN',
      bufferSize: 30,
      syncBuffer: 0.5,
      enableCache: true,
      enableDebug: false,
      ...options
    };

    // 核心组件
    this.platformManager = new PlatformManager();
    this.translationEngine = new TranslationEngine();
    this.audioEngine = new AudioEngine();
    this.cacheManager = new CacheManager();
    this.middlewareManager = new MiddlewareManager();
    this.logger = new Logger(this.options.enableDebug);

    // 状态管理
    this.state = {
      isTranslating: false,
      isPreprocessing: false,
      currentMode: null, // 'subtitle' | 'audio'
      currentPlatform: null,
      translationResults: [],
      preprocessBuffer: new Map()
    };

    this.init();
  }

  /**
   * 初始化系统
   */
  async init() {
    try {
      this.logger.info('ChronoTranslate initializing...');
      
      // 初始化各个组件
      await this.platformManager.init();
      await this.translationEngine.init();
      await this.audioEngine.init();
      await this.cacheManager.init();

      // 注册事件监听器
      this.setupEventListeners();

      // 检测当前平台
      await this.detectCurrentPlatform();

      this.logger.info('ChronoTranslate initialized successfully');
      this.emit('initialized');
      
    } catch (error) {
      this.logger.error('Failed to initialize ChronoTranslate:', error);
      this.emit('error', error);
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 平台事件
    this.platformManager.on('platformDetected', (platform) => {
      this.state.currentPlatform = platform;
      this.emit('platformChanged', platform);
    });

    this.platformManager.on('videoDetected', (videoInfo) => {
      this.emit('videoDetected', videoInfo);
    });

    // 翻译引擎事件
    this.translationEngine.on('translationComplete', (result) => {
      this.handleTranslationResult(result);
    });

    this.translationEngine.on('error', (error) => {
      this.logger.error('Translation error:', error);
      this.emit('translationError', error);
    });

    // 音频引擎事件
    this.audioEngine.on('audioReady', (audioData) => {
      this.handleAudioReady(audioData);
    });

    this.audioEngine.on('audioSegmentReady', (segmentData) => {
      this.handleAudioSegmentReady(segmentData);
    });
  }

  /**
   * 检测当前平台
   */
  async detectCurrentPlatform() {
    const platform = await this.platformManager.detectPlatform(window.location.href);
    if (platform) {
      this.state.currentPlatform = platform;
      this.logger.info(`Platform detected: ${platform.name}`);
      
      // 初始化平台特定功能
      await platform.initialize();
      
      this.emit('platformDetected', platform);
    }
  }

  /**
   * 开始翻译
   */
  async startTranslation(options = {}) {
    if (this.state.isTranslating) {
      this.logger.warn('Translation already in progress');
      return;
    }

    if (!this.state.currentPlatform) {
      throw new Error('No supported platform detected');
    }

    try {
      this.state.isTranslating = true;
      this.emit('translationStarted');

      // 应用中间件
      const context = {
        platform: this.state.currentPlatform,
        options: { ...this.options, ...options },
        state: this.state
      };

      await this.middlewareManager.execute('beforeTranslation', context);

      // 检测翻译模式
      const mode = await this.detectTranslationMode();
      this.state.currentMode = mode;

      this.logger.info(`Starting translation in ${mode} mode`);

      if (mode === 'subtitle') {
        await this.startSubtitleMode(context);
      } else {
        await this.startAudioMode(context);
      }

      await this.middlewareManager.execute('afterTranslationStart', context);

    } catch (error) {
      this.state.isTranslating = false;
      this.logger.error('Failed to start translation:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 停止翻译
   */
  async stopTranslation() {
    if (!this.state.isTranslating) {
      return;
    }

    try {
      this.state.isTranslating = false;
      this.state.isPreprocessing = false;
      this.state.currentMode = null;

      // 停止音频引擎
      await this.audioEngine.stop();

      // 清理状态
      this.state.translationResults = [];
      this.state.preprocessBuffer.clear();

      // 应用中间件
      const context = { state: this.state };
      await this.middlewareManager.execute('afterTranslationStop', context);

      this.logger.info('Translation stopped');
      this.emit('translationStopped');

    } catch (error) {
      this.logger.error('Error stopping translation:', error);
      this.emit('error', error);
    }
  }

  /**
   * 检测翻译模式
   */
  async detectTranslationMode() {
    if (!this.state.currentPlatform) {
      throw new Error('No platform detected');
    }

    const hasSubtitles = await this.state.currentPlatform.hasSubtitles();
    return hasSubtitles ? 'subtitle' : 'audio';
  }

  /**
   * 字幕模式翻译
   */
  async startSubtitleMode(context) {
    this.state.isPreprocessing = true;
    this.emit('statusUpdate', { message: '正在预处理字幕...', type: 'loading' });

    try {
      // 检查缓存
      const cacheKey = this.cacheManager.generateKey(
        this.state.currentPlatform.getVideoId(),
        this.options.targetLanguage
      );

      const cachedResults = await this.cacheManager.get(cacheKey);
      if (cachedResults) {
        this.state.translationResults = cachedResults;
        this.state.isPreprocessing = false;
        this.emit('statusUpdate', { message: '翻译就绪 (缓存)', type: 'active' });
        this.startSubtitlePlayback();
        return;
      }

      // 提取字幕
      const subtitles = await this.state.currentPlatform.extractSubtitles();
      if (subtitles.length === 0) {
        throw new Error('无法提取字幕数据');
      }

      // 预处理字幕
      await this.preprocessSubtitles(subtitles, context);

      // 缓存结果
      await this.cacheManager.set(cacheKey, this.state.translationResults);

      this.state.isPreprocessing = false;
      this.emit('statusUpdate', { message: '翻译就绪 (无延迟)', type: 'active' });
      
      // 开始播放
      this.startSubtitlePlayback();

    } catch (error) {
      this.state.isPreprocessing = false;
      throw error;
    }
  }

  /**
   * 音频模式翻译
   */
  async startAudioMode(context) {
    this.state.isPreprocessing = true;
    this.emit('statusUpdate', { message: '正在启动音频预处理...', type: 'loading' });

    try {
      // 启动音频引擎
      await this.audioEngine.startCapture({
        platform: this.state.currentPlatform,
        targetLanguage: this.options.targetLanguage,
        bufferSize: this.options.bufferSize
      });

      this.emit('statusUpdate', { message: '音频预处理进行中', type: 'active' });

    } catch (error) {
      this.state.isPreprocessing = false;
      throw error;
    }
  }

  /**
   * 预处理字幕
   */
  async preprocessSubtitles(subtitles, context) {
    this.emit('statusUpdate', { 
      message: `正在处理 ${subtitles.length} 条字幕...`, 
      type: 'loading' 
    });

    // 应用中间件
    await this.middlewareManager.execute('beforeSubtitleProcessing', {
      ...context,
      subtitles
    });

    // 批量翻译
    const translatedTexts = await this.translationEngine.batchTranslate(
      subtitles.map(s => s.text),
      this.options.targetLanguage
    );

    // 并行生成语音
    const results = await Promise.all(
      subtitles.map(async (subtitle, index) => {
        const translatedText = translatedTexts[index];
        const audioUrl = await this.audioEngine.textToSpeech(
          translatedText,
          this.options.targetLanguage
        );

        return {
          id: subtitle.id,
          startTime: subtitle.startTime,
          endTime: subtitle.endTime,
          originalText: subtitle.text,
          translatedText: translatedText,
          audioUrl: audioUrl
        };
      })
    );

    this.state.translationResults = results;

    // 应用中间件
    await this.middlewareManager.execute('afterSubtitleProcessing', {
      ...context,
      results
    });

    this.logger.info(`Preprocessed ${results.length} subtitle segments`);
  }

  /**
   * 开始字幕播放同步
   */
  startSubtitlePlayback() {
    if (!this.state.currentPlatform) return;

    const video = this.state.currentPlatform.getVideoElement();
    if (!video) return;

    // 静音原视频
    video.muted = true;

    // 监听时间更新
    video.addEventListener('timeupdate', () => {
      this.syncAudioPlayback();
    });

    this.emit('playbackStarted');
  }

  /**
   * 音频同步播放
   */
  syncAudioPlayback() {
    if (!this.state.isTranslating || this.state.isPreprocessing) return;

    const video = this.state.currentPlatform.getVideoElement();
    if (!video) return;

    const currentTime = video.currentTime;

    // 查找当前时间对应的翻译音频
    const currentResult = this.state.translationResults.find(result =>
      currentTime >= (result.startTime - this.options.syncBuffer) &&
      currentTime <= (result.endTime + this.options.syncBuffer)
    );

    if (currentResult && currentResult.audioUrl) {
      this.audioEngine.playTranslationAudio(currentResult, currentTime);
    }
  }

  /**
   * 处理翻译结果
   */
  handleTranslationResult(result) {
    this.state.translationResults.push(result);
    this.emit('translationResult', result);
  }

  /**
   * 处理音频就绪
   */
  handleAudioReady(audioData) {
    this.emit('audioReady', audioData);
  }

  /**
   * 处理音频段就绪
   */
  handleAudioSegmentReady(segmentData) {
    const bufferKey = Math.floor(segmentData.startTime);
    this.state.preprocessBuffer.set(bufferKey, segmentData);

    // 如果这是第一个音频段，标记预处理完成
    if (this.state.isPreprocessing && this.state.preprocessBuffer.size >= 3) {
      this.state.isPreprocessing = false;
      this.emit('statusUpdate', { message: '音频翻译 (缓冲就绪)', type: 'active' });
    }

    this.emit('audioSegmentReady', segmentData);
  }

  /**
   * 获取当前状态
   */
  getState() {
    return { ...this.state };
  }

  /**
   * 更新配置
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions };
    this.emit('optionsUpdated', this.options);
  }

  /**
   * 销毁实例
   */
  async destroy() {
    await this.stopTranslation();
    
    // 清理组件
    await this.audioEngine.destroy();
    await this.cacheManager.destroy();
    
    // 移除所有监听器
    this.removeAllListeners();
    
    this.logger.info('ChronoTranslate destroyed');
  }
}
