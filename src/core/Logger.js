/**
 * 日志器
 * 提供统一的日志记录功能
 */

export class Logger {
  constructor(enabled = false) {
    this.enabled = enabled;
    this.logs = [];
    this.maxLogs = 1000;
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    this.currentLevel = this.levels.INFO;
  }

  /**
   * 设置日志级别
   * @param {string} level 日志级别
   */
  setLevel(level) {
    if (this.levels[level.toUpperCase()] !== undefined) {
      this.currentLevel = this.levels[level.toUpperCase()];
    }
  }

  /**
   * 启用/禁用日志
   * @param {boolean} enabled 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }

  /**
   * 记录日志
   * @param {string} level 日志级别
   * @param {string} message 消息
   * @param {...any} args 额外参数
   */
  log(level, message, ...args) {
    const levelValue = this.levels[level.toUpperCase()];
    
    if (!this.enabled || levelValue > this.currentLevel) {
      return;
    }

    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      args,
      stack: level.toUpperCase() === 'ERROR' ? new Error().stack : null
    };

    // 添加到日志数组
    this.logs.push(logEntry);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // 输出到控制台
    this.outputToConsole(logEntry);
  }

  /**
   * 输出到控制台
   * @param {Object} logEntry 日志条目
   */
  outputToConsole(logEntry) {
    const { timestamp, level, message, args } = logEntry;
    const prefix = `[ChronoTranslate ${level}] ${timestamp.split('T')[1].split('.')[0]}`;
    
    switch (level) {
      case 'ERROR':
        console.error(prefix, message, ...args);
        break;
      case 'WARN':
        console.warn(prefix, message, ...args);
        break;
      case 'INFO':
        console.info(prefix, message, ...args);
        break;
      case 'DEBUG':
        console.debug(prefix, message, ...args);
        break;
      default:
        console.log(prefix, message, ...args);
    }
  }

  /**
   * 错误日志
   * @param {string} message 消息
   * @param {...any} args 额外参数
   */
  error(message, ...args) {
    this.log('ERROR', message, ...args);
  }

  /**
   * 警告日志
   * @param {string} message 消息
   * @param {...any} args 额外参数
   */
  warn(message, ...args) {
    this.log('WARN', message, ...args);
  }

  /**
   * 信息日志
   * @param {string} message 消息
   * @param {...any} args 额外参数
   */
  info(message, ...args) {
    this.log('INFO', message, ...args);
  }

  /**
   * 调试日志
   * @param {string} message 消息
   * @param {...any} args 额外参数
   */
  debug(message, ...args) {
    this.log('DEBUG', message, ...args);
  }

  /**
   * 获取所有日志
   * @param {string} level 可选的日志级别过滤
   * @returns {Array}
   */
  getLogs(level = null) {
    if (level) {
      return this.logs.filter(log => log.level === level.toUpperCase());
    }
    return [...this.logs];
  }

  /**
   * 清空日志
   */
  clear() {
    this.logs = [];
  }

  /**
   * 导出日志
   * @param {string} format 导出格式 ('json' | 'text')
   * @returns {string}
   */
  export(format = 'json') {
    if (format === 'json') {
      return JSON.stringify({
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        logs: this.logs
      }, null, 2);
    } else if (format === 'text') {
      return this.logs.map(log => {
        const args = log.args.length > 0 ? ' ' + log.args.map(arg => 
          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
        ).join(' ') : '';
        return `[${log.timestamp}] ${log.level}: ${log.message}${args}`;
      }).join('\n');
    }
    
    return '';
  }

  /**
   * 下载日志文件
   * @param {string} format 文件格式
   */
  download(format = 'json') {
    const content = this.export(format);
    const blob = new Blob([content], { 
      type: format === 'json' ? 'application/json' : 'text/plain' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chronotranslate-logs-${Date.now()}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    const stats = {
      total: this.logs.length,
      byLevel: {}
    };

    Object.keys(this.levels).forEach(level => {
      stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
    });

    return stats;
  }

  /**
   * 创建子日志器
   * @param {string} prefix 前缀
   * @returns {Object}
   */
  createChild(prefix) {
    return {
      error: (message, ...args) => this.error(`[${prefix}] ${message}`, ...args),
      warn: (message, ...args) => this.warn(`[${prefix}] ${message}`, ...args),
      info: (message, ...args) => this.info(`[${prefix}] ${message}`, ...args),
      debug: (message, ...args) => this.debug(`[${prefix}] ${message}`, ...args)
    };
  }
}
