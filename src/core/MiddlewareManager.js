/**
 * 中间件管理器
 * 支持在翻译流程的各个阶段插入自定义逻辑
 */

import { EventEmitter } from './EventEmitter.js';

export class MiddlewareManager extends EventEmitter {
  constructor() {
    super();
    this.middlewares = new Map();
    this.hooks = [
      'beforeTranslation',
      'afterTranslationStart',
      'beforeSubtitleProcessing',
      'afterSubtitleProcessing',
      'beforeAudioProcessing',
      'afterAudioProcessing',
      'beforeTextTranslation',
      'afterTextTranslation',
      'beforeTTS',
      'afterTTS',
      'beforeSTT',
      'afterSTT',
      'afterTranslationStop'
    ];
    
    // 为每个钩子初始化中间件数组
    this.hooks.forEach(hook => {
      this.middlewares.set(hook, []);
    });
  }

  /**
   * 注册中间件
   * @param {string} hook 钩子名称
   * @param {Function|Object} middleware 中间件函数或对象
   * @param {Object} options 选项
   */
  use(hook, middleware, options = {}) {
    if (!this.hooks.includes(hook)) {
      throw new Error(`Invalid hook: ${hook}. Available hooks: ${this.hooks.join(', ')}`);
    }

    const middlewareObj = {
      id: options.id || `middleware_${Date.now()}_${Math.random()}`,
      name: options.name || 'Anonymous Middleware',
      priority: options.priority || 0,
      enabled: options.enabled !== false,
      middleware: typeof middleware === 'function' ? middleware : middleware.execute,
      options: options
    };

    const hookMiddlewares = this.middlewares.get(hook);
    hookMiddlewares.push(middlewareObj);
    
    // 按优先级排序（高优先级先执行）
    hookMiddlewares.sort((a, b) => b.priority - a.priority);

    this.emit('middlewareRegistered', { hook, middleware: middlewareObj });
    
    return middlewareObj.id;
  }

  /**
   * 移除中间件
   * @param {string} hook 钩子名称
   * @param {string} middlewareId 中间件ID
   */
  remove(hook, middlewareId) {
    if (!this.middlewares.has(hook)) {
      return false;
    }

    const hookMiddlewares = this.middlewares.get(hook);
    const index = hookMiddlewares.findIndex(m => m.id === middlewareId);
    
    if (index !== -1) {
      const removed = hookMiddlewares.splice(index, 1)[0];
      this.emit('middlewareRemoved', { hook, middleware: removed });
      return true;
    }
    
    return false;
  }

  /**
   * 启用/禁用中间件
   * @param {string} hook 钩子名称
   * @param {string} middlewareId 中间件ID
   * @param {boolean} enabled 是否启用
   */
  toggle(hook, middlewareId, enabled) {
    if (!this.middlewares.has(hook)) {
      return false;
    }

    const hookMiddlewares = this.middlewares.get(hook);
    const middleware = hookMiddlewares.find(m => m.id === middlewareId);
    
    if (middleware) {
      middleware.enabled = enabled;
      this.emit('middlewareToggled', { hook, middleware, enabled });
      return true;
    }
    
    return false;
  }

  /**
   * 执行钩子的所有中间件
   * @param {string} hook 钩子名称
   * @param {Object} context 上下文对象
   */
  async execute(hook, context = {}) {
    if (!this.middlewares.has(hook)) {
      return context;
    }

    const hookMiddlewares = this.middlewares.get(hook);
    const enabledMiddlewares = hookMiddlewares.filter(m => m.enabled);
    
    if (enabledMiddlewares.length === 0) {
      return context;
    }

    this.emit('hookExecutionStart', { hook, middlewareCount: enabledMiddlewares.length });

    let currentContext = { ...context };
    const executionResults = [];

    for (const middlewareObj of enabledMiddlewares) {
      try {
        const startTime = Date.now();
        
        this.emit('middlewareExecutionStart', { 
          hook, 
          middleware: middlewareObj,
          context: currentContext 
        });

        // 执行中间件
        const result = await this.executeMiddleware(middlewareObj, currentContext, hook);
        
        const endTime = Date.now();
        const executionTime = endTime - startTime;

        // 更新上下文
        if (result && typeof result === 'object') {
          currentContext = { ...currentContext, ...result };
        }

        const executionResult = {
          middlewareId: middlewareObj.id,
          middlewareName: middlewareObj.name,
          success: true,
          executionTime,
          result
        };

        executionResults.push(executionResult);

        this.emit('middlewareExecutionEnd', { 
          hook, 
          middleware: middlewareObj,
          result: executionResult
        });

      } catch (error) {
        const executionResult = {
          middlewareId: middlewareObj.id,
          middlewareName: middlewareObj.name,
          success: false,
          error: error.message,
          stack: error.stack
        };

        executionResults.push(executionResult);

        this.emit('middlewareExecutionError', { 
          hook, 
          middleware: middlewareObj,
          error,
          context: currentContext
        });

        // 根据中间件配置决定是否继续执行
        if (middlewareObj.options.stopOnError !== false) {
          throw new Error(`Middleware ${middlewareObj.name} failed: ${error.message}`);
        }
      }
    }

    this.emit('hookExecutionEnd', { 
      hook, 
      context: currentContext,
      results: executionResults
    });

    return currentContext;
  }

  /**
   * 执行单个中间件
   * @param {Object} middlewareObj 中间件对象
   * @param {Object} context 上下文
   * @param {string} hook 钩子名称
   */
  async executeMiddleware(middlewareObj, context, hook) {
    const { middleware, options } = middlewareObj;

    // 检查条件
    if (options.condition && !options.condition(context)) {
      return context;
    }

    // 设置超时
    if (options.timeout) {
      return Promise.race([
        middleware(context, hook),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Middleware timeout')), options.timeout)
        )
      ]);
    }

    return await middleware(context, hook);
  }

  /**
   * 获取钩子的中间件列表
   * @param {string} hook 钩子名称
   */
  getMiddlewares(hook) {
    if (!this.middlewares.has(hook)) {
      return [];
    }
    
    return this.middlewares.get(hook).map(m => ({
      id: m.id,
      name: m.name,
      priority: m.priority,
      enabled: m.enabled,
      options: m.options
    }));
  }

  /**
   * 获取所有钩子和中间件
   */
  getAllMiddlewares() {
    const result = {};
    
    for (const hook of this.hooks) {
      result[hook] = this.getMiddlewares(hook);
    }
    
    return result;
  }

  /**
   * 清空钩子的所有中间件
   * @param {string} hook 钩子名称
   */
  clear(hook) {
    if (this.middlewares.has(hook)) {
      this.middlewares.set(hook, []);
      this.emit('hookCleared', { hook });
    }
  }

  /**
   * 清空所有中间件
   */
  clearAll() {
    this.hooks.forEach(hook => {
      this.middlewares.set(hook, []);
    });
    this.emit('allMiddlewaresCleared');
  }

  /**
   * 注册内置中间件
   */
  registerBuiltinMiddlewares() {
    // 日志中间件
    this.use('beforeTranslation', async (context) => {
      console.log('ChronoTranslate: Starting translation', {
        platform: context.platform?.name,
        mode: context.options?.mode,
        targetLanguage: context.options?.targetLanguage
      });
      return context;
    }, { name: 'Logger', priority: 1000 });

    // 性能监控中间件
    this.use('beforeTranslation', async (context) => {
      context.startTime = Date.now();
      return context;
    }, { name: 'Performance Monitor Start', priority: 999 });

    this.use('afterTranslationStop', async (context) => {
      if (context.startTime) {
        const duration = Date.now() - context.startTime;
        console.log(`ChronoTranslate: Translation completed in ${duration}ms`);
      }
      return context;
    }, { name: 'Performance Monitor End', priority: 999 });

    // 错误处理中间件
    this.use('beforeTranslation', async (context) => {
      if (!context.platform) {
        throw new Error('No platform detected');
      }
      if (!context.options?.targetLanguage) {
        throw new Error('Target language not specified');
      }
      return context;
    }, { name: 'Validation', priority: 998 });
  }

  /**
   * 获取中间件执行统计
   */
  getStatistics() {
    const stats = {
      totalHooks: this.hooks.length,
      totalMiddlewares: 0,
      enabledMiddlewares: 0,
      hookStats: {}
    };

    for (const hook of this.hooks) {
      const middlewares = this.middlewares.get(hook);
      const enabled = middlewares.filter(m => m.enabled);
      
      stats.totalMiddlewares += middlewares.length;
      stats.enabledMiddlewares += enabled.length;
      
      stats.hookStats[hook] = {
        total: middlewares.length,
        enabled: enabled.length,
        middlewares: middlewares.map(m => ({
          id: m.id,
          name: m.name,
          enabled: m.enabled
        }))
      };
    }

    return stats;
  }

  /**
   * 销毁中间件管理器
   */
  destroy() {
    this.clearAll();
    this.removeAllListeners();
  }
}
