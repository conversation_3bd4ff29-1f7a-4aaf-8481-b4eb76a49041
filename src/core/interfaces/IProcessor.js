/**
 * 处理器接口
 * 定义所有处理器必须实现的基础接口
 */

export class IProcessor {
  /**
   * 处理数据的核心方法
   * @param {any} input 输入数据
   * @param {Object} options 处理选项
   * @returns {Promise<any>} 处理结果
   */
  async process(input, options = {}) {
    throw new Error('process method must be implemented');
  }

  /**
   * 获取处理器名称
   * @returns {string}
   */
  getName() {
    throw new Error('getName method must be implemented');
  }

  /**
   * 检查处理器是否可用
   * @returns {boolean}
   */
  isAvailable() {
    throw new Error('isAvailable method must be implemented');
  }

  /**
   * 获取处理器配置
   * @returns {Object}
   */
  getConfig() {
    return {};
  }

  /**
   * 更新处理器配置
   * @param {Object} config 新配置
   */
  updateConfig(config) {
    // 子类可以重写此方法
  }

  /**
   * 验证输入数据
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {boolean}
   */
  validateInput(input, options = {}) {
    return input !== null && input !== undefined;
  }

  /**
   * 预处理输入数据
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {any} 预处理后的数据
   */
  preprocessInput(input, options = {}) {
    return input;
  }

  /**
   * 后处理输出数据
   * @param {any} output 输出数据
   * @param {Object} options 选项
   * @returns {any} 后处理后的数据
   */
  postprocessOutput(output, options = {}) {
    return output;
  }

  /**
   * 处理错误
   * @param {Error} error 错误对象
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {any} 错误处理结果
   */
  handleError(error, input, options = {}) {
    throw error;
  }

  /**
   * 获取处理器统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      name: this.getName(),
      available: this.isAvailable(),
      config: this.getConfig()
    };
  }
}

/**
 * 批处理器接口
 * 支持批量处理的处理器接口
 */
export class IBatchProcessor extends IProcessor {
  /**
   * 批量处理数据
   * @param {Array} inputs 输入数据数组
   * @param {Object} options 处理选项
   * @returns {Promise<Array>} 处理结果数组
   */
  async batchProcess(inputs, options = {}) {
    throw new Error('batchProcess method must be implemented');
  }

  /**
   * 获取最佳批处理大小
   * @returns {number}
   */
  getOptimalBatchSize() {
    return 10;
  }

  /**
   * 检查是否支持批处理
   * @returns {boolean}
   */
  supportsBatch() {
    return true;
  }
}

/**
 * 流处理器接口
 * 支持流式处理的处理器接口
 */
export class IStreamProcessor extends IProcessor {
  /**
   * 开始流处理
   * @param {Object} options 处理选项
   * @returns {Promise<ReadableStream>}
   */
  async startStream(options = {}) {
    throw new Error('startStream method must be implemented');
  }

  /**
   * 处理流数据块
   * @param {any} chunk 数据块
   * @param {Object} options 选项
   * @returns {Promise<any>}
   */
  async processChunk(chunk, options = {}) {
    throw new Error('processChunk method must be implemented');
  }

  /**
   * 结束流处理
   * @param {Object} options 选项
   * @returns {Promise<void>}
   */
  async endStream(options = {}) {
    // 子类可以重写此方法
  }
}
