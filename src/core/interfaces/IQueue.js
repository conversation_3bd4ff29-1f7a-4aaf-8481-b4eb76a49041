/**
 * 队列接口
 * 定义所有队列必须实现的基础接口
 */

import { EventEmitter } from '../EventEmitter.js';

export class IQueue extends EventEmitter {
  /**
   * 将任务加入队列
   * @param {Object} task 任务对象
   * @returns {Promise<string>} 任务ID
   */
  async enqueue(task) {
    throw new Error('enqueue method must be implemented');
  }

  /**
   * 从队列中取出任务
   * @returns {Promise<Object|null>} 任务对象或null
   */
  async dequeue() {
    throw new Error('dequeue method must be implemented');
  }

  /**
   * 查看队列头部任务但不移除
   * @returns {Promise<Object|null>} 任务对象或null
   */
  async peek() {
    throw new Error('peek method must be implemented');
  }

  /**
   * 获取队列大小
   * @returns {number}
   */
  size() {
    throw new Error('size method must be implemented');
  }

  /**
   * 检查队列是否为空
   * @returns {boolean}
   */
  isEmpty() {
    return this.size() === 0;
  }

  /**
   * 清空队列
   * @returns {Promise<void>}
   */
  async clear() {
    throw new Error('clear method must be implemented');
  }

  /**
   * 获取队列中的所有任务
   * @returns {Array<Object>}
   */
  getAllTasks() {
    throw new Error('getAllTasks method must be implemented');
  }

  /**
   * 根据ID查找任务
   * @param {string} taskId 任务ID
   * @returns {Object|null}
   */
  findTask(taskId) {
    throw new Error('findTask method must be implemented');
  }

  /**
   * 移除指定任务
   * @param {string} taskId 任务ID
   * @returns {Promise<boolean>} 是否成功移除
   */
  async removeTask(taskId) {
    throw new Error('removeTask method must be implemented');
  }

  /**
   * 获取队列统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      size: this.size(),
      isEmpty: this.isEmpty(),
      type: this.constructor.name
    };
  }
}

/**
 * 优先级队列接口
 */
export class IPriorityQueue extends IQueue {
  /**
   * 将任务加入优先级队列
   * @param {Object} task 任务对象
   * @param {number} priority 优先级（数字越大优先级越高）
   * @returns {Promise<string>} 任务ID
   */
  async enqueueWithPriority(task, priority) {
    throw new Error('enqueueWithPriority method must be implemented');
  }

  /**
   * 获取最高优先级任务
   * @returns {Promise<Object|null>}
   */
  async dequeueHighestPriority() {
    throw new Error('dequeueHighestPriority method must be implemented');
  }

  /**
   * 更新任务优先级
   * @param {string} taskId 任务ID
   * @param {number} newPriority 新优先级
   * @returns {Promise<boolean>}
   */
  async updatePriority(taskId, newPriority) {
    throw new Error('updatePriority method must be implemented');
  }
}

/**
 * 批处理队列接口
 */
export class IBatchQueue extends IQueue {
  /**
   * 批量加入任务
   * @param {Array<Object>} tasks 任务数组
   * @returns {Promise<Array<string>>} 任务ID数组
   */
  async enqueueBatch(tasks) {
    throw new Error('enqueueBatch method must be implemented');
  }

  /**
   * 批量取出任务
   * @param {number} count 取出数量
   * @returns {Promise<Array<Object>>} 任务数组
   */
  async dequeueBatch(count) {
    throw new Error('dequeueBatch method must be implemented');
  }

  /**
   * 获取最佳批处理大小
   * @returns {number}
   */
  getOptimalBatchSize() {
    return 10;
  }

  /**
   * 设置批处理大小
   * @param {number} size 批处理大小
   */
  setBatchSize(size) {
    throw new Error('setBatchSize method must be implemented');
  }
}

/**
 * 延迟队列接口
 */
export class IDelayQueue extends IQueue {
  /**
   * 将任务加入延迟队列
   * @param {Object} task 任务对象
   * @param {number} delay 延迟时间（毫秒）
   * @returns {Promise<string>} 任务ID
   */
  async enqueueWithDelay(task, delay) {
    throw new Error('enqueueWithDelay method must be implemented');
  }

  /**
   * 将任务加入定时队列
   * @param {Object} task 任务对象
   * @param {Date} executeAt 执行时间
   * @returns {Promise<string>} 任务ID
   */
  async enqueueAt(task, executeAt) {
    throw new Error('enqueueAt method must be implemented');
  }

  /**
   * 获取下一个可执行任务
   * @returns {Promise<Object|null>}
   */
  async getNextExecutableTask() {
    throw new Error('getNextExecutableTask method must be implemented');
  }
}

/**
 * 任务状态枚举
 */
export const TaskStatus = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 任务优先级枚举
 */
export const TaskPriority = {
  LOW: 1,
  NORMAL: 5,
  HIGH: 10,
  URGENT: 20
};
