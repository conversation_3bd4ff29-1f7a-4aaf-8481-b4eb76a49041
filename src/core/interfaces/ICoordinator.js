/**
 * 协调器接口
 * 定义所有协调器必须实现的基础接口
 */

import { EventEmitter } from '../EventEmitter.js';

export class ICoordinator extends EventEmitter {
  /**
   * 初始化协调器
   * @param {Object} config 配置对象
   * @returns {Promise<void>}
   */
  async initialize(config = {}) {
    throw new Error('initialize method must be implemented');
  }

  /**
   * 启动协调器
   * @param {Object} options 启动选项
   * @returns {Promise<void>}
   */
  async start(options = {}) {
    throw new Error('start method must be implemented');
  }

  /**
   * 停止协调器
   * @returns {Promise<void>}
   */
  async stop() {
    throw new Error('stop method must be implemented');
  }

  /**
   * 暂停协调器
   * @returns {Promise<void>}
   */
  async pause() {
    throw new Error('pause method must be implemented');
  }

  /**
   * 恢复协调器
   * @returns {Promise<void>}
   */
  async resume() {
    throw new Error('resume method must be implemented');
  }

  /**
   * 获取协调器状态
   * @returns {string}
   */
  getStatus() {
    throw new Error('getStatus method must be implemented');
  }

  /**
   * 更新配置
   * @param {Object} config 新配置
   * @returns {Promise<void>}
   */
  async updateConfig(config) {
    throw new Error('updateConfig method must be implemented');
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    throw new Error('getStatistics method must be implemented');
  }

  /**
   * 销毁协调器
   * @returns {Promise<void>}
   */
  async destroy() {
    throw new Error('destroy method must be implemented');
  }
}

/**
 * 翻译协调器接口
 */
export class ITranslationCoordinator extends ICoordinator {
  /**
   * 开始翻译任务
   * @param {Array} subtitles 字幕数组
   * @param {Object} options 翻译选项
   * @returns {Promise<string>} 任务ID
   */
  async startTranslation(subtitles, options = {}) {
    throw new Error('startTranslation method must be implemented');
  }

  /**
   * 停止翻译任务
   * @param {string} taskId 任务ID
   * @returns {Promise<void>}
   */
  async stopTranslation(taskId) {
    throw new Error('stopTranslation method must be implemented');
  }

  /**
   * 获取翻译进度
   * @param {string} taskId 任务ID
   * @returns {Object}
   */
  getTranslationProgress(taskId) {
    throw new Error('getTranslationProgress method must be implemented');
  }

  /**
   * 获取翻译结果
   * @param {string} taskId 任务ID
   * @returns {Promise<Array>}
   */
  async getTranslationResult(taskId) {
    throw new Error('getTranslationResult method must be implemented');
  }
}

/**
 * 音频协调器接口
 */
export class IAudioCoordinator extends ICoordinator {
  /**
   * 开始音频处理任务
   * @param {Array} audioTasks 音频任务数组
   * @param {Object} options 处理选项
   * @returns {Promise<string>} 任务ID
   */
  async startAudioProcessing(audioTasks, options = {}) {
    throw new Error('startAudioProcessing method must be implemented');
  }

  /**
   * 停止音频处理任务
   * @param {string} taskId 任务ID
   * @returns {Promise<void>}
   */
  async stopAudioProcessing(taskId) {
    throw new Error('stopAudioProcessing method must be implemented');
  }

  /**
   * 获取音频处理进度
   * @param {string} taskId 任务ID
   * @returns {Object}
   */
  getAudioProgress(taskId) {
    throw new Error('getAudioProgress method must be implemented');
  }

  /**
   * 获取音频处理结果
   * @param {string} taskId 任务ID
   * @returns {Promise<Array>}
   */
  async getAudioResult(taskId) {
    throw new Error('getAudioResult method must be implemented');
  }
}

/**
 * 协调器状态枚举
 */
export const CoordinatorStatus = {
  IDLE: 'idle',
  INITIALIZING: 'initializing',
  RUNNING: 'running',
  PAUSED: 'paused',
  STOPPING: 'stopping',
  STOPPED: 'stopped',
  ERROR: 'error'
};

/**
 * 任务类型枚举
 */
export const TaskType = {
  TRANSLATION: 'translation',
  TTS: 'tts',
  STT: 'stt',
  SUBTITLE_EXTRACTION: 'subtitle_extraction',
  AUDIO_SYNC: 'audio_sync'
};
