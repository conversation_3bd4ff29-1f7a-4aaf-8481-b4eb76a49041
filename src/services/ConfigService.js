/**
 * 配置服务
 * 管理应用配置的加载、保存和验证
 */

export class ConfigService {
  constructor() {
    this.storageKey = 'chronotranslate_config';
    this.cache = null;
    
    // 默认配置
    this.defaultConfig = {
      // 翻译设置
      targetLanguage: 'zh-CN',
      translationProvider: 'google',
      translationApiKey: '',
      
      // TTS设置
      enableTTS: true,
      ttsProvider: 'google',
      ttsApiKey: '',
      voice: 'default',
      speakingRate: 1.0,
      pitch: 0.0,
      volume: 1.0,
      
      // UI设置
      theme: 'auto',
      showProgress: true,
      autoStart: false,
      
      // 高级设置
      batchSize: 10,
      maxConcurrency: 3,
      timeout: 30000,
      retryAttempts: 3,
      
      // 平台设置
      enabledPlatforms: ['youtube', 'tiktok'],
      
      // 缓存设置
      enableCache: true,
      cacheSize: 100,
      cacheTTL: 86400000 // 24小时
    };
  }

  /**
   * 加载配置
   * @returns {Promise<Object>}
   */
  async load() {
    if (this.cache) {
      return { ...this.cache };
    }

    try {
      // 从Chrome存储加载配置
      const result = await chrome.storage.sync.get(this.storageKey);
      const savedConfig = result[this.storageKey] || {};
      
      // 合并默认配置和保存的配置
      this.cache = { ...this.defaultConfig, ...savedConfig };
      
      // 验证配置
      this.validateConfig(this.cache);
      
      return { ...this.cache };

    } catch (error) {
      console.error('Failed to load config:', error);
      
      // 如果加载失败，返回默认配置
      this.cache = { ...this.defaultConfig };
      return { ...this.cache };
    }
  }

  /**
   * 保存配置
   * @param {Object} config 要保存的配置
   * @returns {Promise<void>}
   */
  async save(config) {
    try {
      // 验证配置
      this.validateConfig(config);
      
      // 保存到Chrome存储
      await chrome.storage.sync.set({
        [this.storageKey]: config
      });
      
      // 更新缓存
      this.cache = { ...config };

    } catch (error) {
      console.error('Failed to save config:', error);
      throw error;
    }
  }

  /**
   * 更新配置
   * @param {Object} updates 要更新的配置项
   * @returns {Promise<Object>} 更新后的完整配置
   */
  async update(updates) {
    const currentConfig = await this.load();
    const newConfig = { ...currentConfig, ...updates };
    
    await this.save(newConfig);
    return newConfig;
  }

  /**
   * 重置配置为默认值
   * @returns {Promise<Object>}
   */
  async reset() {
    const defaultConfig = { ...this.defaultConfig };
    await this.save(defaultConfig);
    return defaultConfig;
  }

  /**
   * 获取特定配置项
   * @param {string} key 配置键
   * @param {any} defaultValue 默认值
   * @returns {Promise<any>}
   */
  async get(key, defaultValue = null) {
    const config = await this.load();
    return config[key] !== undefined ? config[key] : defaultValue;
  }

  /**
   * 设置特定配置项
   * @param {string} key 配置键
   * @param {any} value 配置值
   * @returns {Promise<void>}
   */
  async set(key, value) {
    await this.update({ [key]: value });
  }

  /**
   * 验证配置
   * @param {Object} config 要验证的配置
   * @throws {Error} 如果配置无效
   */
  validateConfig(config) {
    // 验证目标语言
    if (config.targetLanguage && !this.isValidLanguageCode(config.targetLanguage)) {
      throw new Error(`Invalid target language: ${config.targetLanguage}`);
    }

    // 验证翻译提供商
    if (config.translationProvider && !this.isValidProvider(config.translationProvider)) {
      throw new Error(`Invalid translation provider: ${config.translationProvider}`);
    }

    // 验证TTS提供商
    if (config.ttsProvider && !this.isValidProvider(config.ttsProvider)) {
      throw new Error(`Invalid TTS provider: ${config.ttsProvider}`);
    }

    // 验证数值范围
    if (config.speakingRate !== undefined) {
      if (typeof config.speakingRate !== 'number' || config.speakingRate < 0.25 || config.speakingRate > 4.0) {
        throw new Error('Speaking rate must be between 0.25 and 4.0');
      }
    }

    if (config.pitch !== undefined) {
      if (typeof config.pitch !== 'number' || config.pitch < -20.0 || config.pitch > 20.0) {
        throw new Error('Pitch must be between -20.0 and 20.0');
      }
    }

    if (config.volume !== undefined) {
      if (typeof config.volume !== 'number' || config.volume < 0.0 || config.volume > 1.0) {
        throw new Error('Volume must be between 0.0 and 1.0');
      }
    }

    if (config.batchSize !== undefined) {
      if (!Number.isInteger(config.batchSize) || config.batchSize < 1 || config.batchSize > 100) {
        throw new Error('Batch size must be an integer between 1 and 100');
      }
    }

    if (config.maxConcurrency !== undefined) {
      if (!Number.isInteger(config.maxConcurrency) || config.maxConcurrency < 1 || config.maxConcurrency > 10) {
        throw new Error('Max concurrency must be an integer between 1 and 10');
      }
    }

    if (config.timeout !== undefined) {
      if (!Number.isInteger(config.timeout) || config.timeout < 1000 || config.timeout > 300000) {
        throw new Error('Timeout must be an integer between 1000 and 300000 milliseconds');
      }
    }
  }

  /**
   * 检查是否为有效的语言代码
   * @param {string} languageCode 语言代码
   * @returns {boolean}
   */
  isValidLanguageCode(languageCode) {
    const validLanguages = [
      'zh-CN', 'zh-TW', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'ru', 'pt', 'it',
      'ar', 'hi', 'th', 'vi', 'nl', 'pl', 'tr', 'sv', 'da', 'no', 'fi'
    ];
    return validLanguages.includes(languageCode);
  }

  /**
   * 检查是否为有效的提供商
   * @param {string} provider 提供商名称
   * @returns {boolean}
   */
  isValidProvider(provider) {
    const validProviders = ['google', 'openai', 'azure', 'baidu', 'tencent'];
    return validProviders.includes(provider);
  }

  /**
   * 获取支持的语言列表
   * @returns {Array}
   */
  getSupportedLanguages() {
    return [
      { code: 'zh-CN', name: '中文 (简体)', native: '中文 (简体)' },
      { code: 'zh-TW', name: '中文 (繁體)', native: '中文 (繁體)' },
      { code: 'en', name: 'English', native: 'English' },
      { code: 'ja', name: '日本語', native: '日本語' },
      { code: 'ko', name: '한국어', native: '한국어' },
      { code: 'es', name: 'Español', native: 'Español' },
      { code: 'fr', name: 'Français', native: 'Français' },
      { code: 'de', name: 'Deutsch', native: 'Deutsch' },
      { code: 'ru', name: 'Русский', native: 'Русский' },
      { code: 'pt', name: 'Português', native: 'Português' },
      { code: 'it', name: 'Italiano', native: 'Italiano' },
      { code: 'ar', name: 'العربية', native: 'العربية' },
      { code: 'hi', name: 'हिन्दी', native: 'हिन्दी' },
      { code: 'th', name: 'ไทย', native: 'ไทย' },
      { code: 'vi', name: 'Tiếng Việt', native: 'Tiếng Việt' },
      { code: 'nl', name: 'Nederlands', native: 'Nederlands' },
      { code: 'pl', name: 'Polski', native: 'Polski' },
      { code: 'tr', name: 'Türkçe', native: 'Türkçe' },
      { code: 'sv', name: 'Svenska', native: 'Svenska' },
      { code: 'da', name: 'Dansk', native: 'Dansk' },
      { code: 'no', name: 'Norsk', native: 'Norsk' },
      { code: 'fi', name: 'Suomi', native: 'Suomi' }
    ];
  }

  /**
   * 获取支持的提供商列表
   * @returns {Array}
   */
  getSupportedProviders() {
    return [
      { 
        id: 'google', 
        name: 'Google', 
        translation: true, 
        tts: true, 
        stt: true,
        description: 'Google Cloud Translation & Text-to-Speech'
      },
      { 
        id: 'openai', 
        name: 'OpenAI', 
        translation: true, 
        tts: true, 
        stt: true,
        description: 'OpenAI GPT & Whisper'
      },
      { 
        id: 'azure', 
        name: 'Microsoft Azure', 
        translation: true, 
        tts: true, 
        stt: true,
        description: 'Azure Cognitive Services'
      },
      { 
        id: 'baidu', 
        name: '百度', 
        translation: true, 
        tts: true, 
        stt: true,
        description: '百度翻译 & 语音合成'
      },
      { 
        id: 'tencent', 
        name: '腾讯', 
        translation: true, 
        tts: true, 
        stt: true,
        description: '腾讯机器翻译 & 语音合成'
      }
    ];
  }

  /**
   * 导出配置
   * @returns {Promise<string>} JSON格式的配置字符串
   */
  async export() {
    const config = await this.load();
    
    // 移除敏感信息（API密钥）
    const exportConfig = { ...config };
    delete exportConfig.translationApiKey;
    delete exportConfig.ttsApiKey;
    
    return JSON.stringify(exportConfig, null, 2);
  }

  /**
   * 导入配置
   * @param {string} configJson JSON格式的配置字符串
   * @returns {Promise<Object>} 导入后的配置
   */
  async import(configJson) {
    try {
      const importedConfig = JSON.parse(configJson);
      
      // 验证导入的配置
      this.validateConfig(importedConfig);
      
      // 合并当前配置（保留API密钥）
      const currentConfig = await this.load();
      const newConfig = {
        ...importedConfig,
        translationApiKey: currentConfig.translationApiKey,
        ttsApiKey: currentConfig.ttsApiKey
      };
      
      await this.save(newConfig);
      return newConfig;

    } catch (error) {
      throw new Error(`Failed to import config: ${error.message}`);
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache = null;
  }
}
