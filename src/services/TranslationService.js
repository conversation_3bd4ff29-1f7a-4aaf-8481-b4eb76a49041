/**
 * 翻译服务
 * 提供简洁的对外API，隐藏内部复杂性
 */

import { EventEmitter } from '../core/EventEmitter.js';
import { TranslationCoordinator } from '../coordinators/TranslationCoordinator.js';
import { ConfigService } from './ConfigService.js';

export class TranslationService extends EventEmitter {
  constructor() {
    super();
    
    this.coordinator = null;
    this.configService = new ConfigService();
    this.initialized = false;
    this.currentTask = null;
    
    // 默认配置
    this.defaultConfig = {
      targetLanguage: 'zh-CN',
      enableTTS: true,
      voice: 'default',
      translationProvider: 'google',
      ttsProvider: 'google'
    };
  }

  /**
   * 初始化服务
   * @param {Object} config 配置对象
   * @returns {Promise<void>}
   */
  async initialize(config = {}) {
    try {
      // 加载配置
      const savedConfig = await this.configService.load();
      const finalConfig = { ...this.defaultConfig, ...savedConfig, ...config };

      // 创建协调器
      this.coordinator = new TranslationCoordinator({
        translation: {
          apiKey: finalConfig.translationApiKey
        },
        tts: {
          apiKey: finalConfig.ttsApiKey
        },
        enableTTS: finalConfig.enableTTS
      });

      // 绑定协调器事件
      this.setupCoordinatorEvents();

      // 初始化协调器
      await this.coordinator.initialize();
      await this.coordinator.start();

      this.initialized = true;
      this.emit('initialized');

    } catch (error) {
      console.error('TranslationService initialization failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 设置协调器事件监听
   */
  setupCoordinatorEvents() {
    this.coordinator.on('translationStarted', (event) => {
      this.emit('translationStarted', event);
    });

    this.coordinator.on('translationProgress', (event) => {
      this.emit('translationProgress', event);
    });

    this.coordinator.on('translationCompleted', (event) => {
      this.currentTask = null;
      this.emit('translationCompleted', event);
    });

    this.coordinator.on('translationFailed', (event) => {
      this.currentTask = null;
      this.emit('translationFailed', event);
    });

    this.coordinator.on('ttsProgress', (event) => {
      this.emit('ttsProgress', event);
    });

    this.coordinator.on('error', (error) => {
      this.emit('error', error);
    });
  }

  /**
   * 翻译视频字幕
   * @param {HTMLVideoElement|Array} videoOrSubtitles 视频元素或字幕数组
   * @param {Object} options 翻译选项
   * @returns {Promise<string>} 任务ID
   */
  async translateVideo(videoOrSubtitles, options = {}) {
    if (!this.initialized) {
      throw new Error('TranslationService not initialized');
    }

    if (this.currentTask) {
      throw new Error('Another translation task is already running');
    }

    try {
      let subtitles;

      // 如果传入的是视频元素，提取字幕
      if (videoOrSubtitles instanceof HTMLVideoElement) {
        subtitles = await this.extractSubtitles(videoOrSubtitles);
      } else if (Array.isArray(videoOrSubtitles)) {
        subtitles = videoOrSubtitles;
      } else {
        throw new Error('Invalid input: expected HTMLVideoElement or subtitle array');
      }

      if (!subtitles || subtitles.length === 0) {
        throw new Error('No subtitles found');
      }

      // 合并配置
      const config = await this.configService.load();
      const translationOptions = {
        targetLanguage: config.targetLanguage || 'zh-CN',
        enableTTS: config.enableTTS !== false,
        voice: config.voice || 'default',
        ...options
      };

      // 开始翻译
      const taskId = await this.coordinator.startTranslation(subtitles, translationOptions);
      this.currentTask = taskId;

      return taskId;

    } catch (error) {
      console.error('Translation failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 停止当前翻译任务
   * @returns {Promise<void>}
   */
  async stopTranslation() {
    if (!this.currentTask) {
      return;
    }

    try {
      await this.coordinator.stopTranslation(this.currentTask);
      this.currentTask = null;
      this.emit('translationStopped');
    } catch (error) {
      console.error('Stop translation failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 暂停翻译
   * @returns {Promise<void>}
   */
  async pauseTranslation() {
    if (!this.coordinator) {
      return;
    }

    try {
      await this.coordinator.pause();
      this.emit('translationPaused');
    } catch (error) {
      console.error('Pause translation failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 恢复翻译
   * @returns {Promise<void>}
   */
  async resumeTranslation() {
    if (!this.coordinator) {
      return;
    }

    try {
      await this.coordinator.resume();
      this.emit('translationResumed');
    } catch (error) {
      console.error('Resume translation failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 获取翻译进度
   * @param {string} taskId 任务ID（可选，默认使用当前任务）
   * @returns {Object|null}
   */
  getProgress(taskId = null) {
    if (!this.coordinator) {
      return null;
    }

    const targetTaskId = taskId || this.currentTask;
    if (!targetTaskId) {
      return null;
    }

    return this.coordinator.getTranslationProgress(targetTaskId);
  }

  /**
   * 获取翻译结果
   * @param {string} taskId 任务ID（可选，默认使用当前任务）
   * @returns {Promise<Array|null>}
   */
  async getResult(taskId = null) {
    if (!this.coordinator) {
      return null;
    }

    const targetTaskId = taskId || this.currentTask;
    if (!targetTaskId) {
      return null;
    }

    return await this.coordinator.getTranslationResult(targetTaskId);
  }

  /**
   * 更新配置
   * @param {Object} config 新配置
   * @returns {Promise<void>}
   */
  async updateConfig(config) {
    try {
      // 保存配置
      await this.configService.update(config);

      // 更新协调器配置
      if (this.coordinator) {
        const coordinatorConfig = {};
        
        if (config.translationApiKey) {
          coordinatorConfig.translation = { apiKey: config.translationApiKey };
        }
        
        if (config.ttsApiKey) {
          coordinatorConfig.tts = { apiKey: config.ttsApiKey };
        }
        
        if (config.enableTTS !== undefined) {
          coordinatorConfig.enableTTS = config.enableTTS;
        }

        await this.coordinator.updateConfig(coordinatorConfig);
      }

      this.emit('configUpdated', config);

    } catch (error) {
      console.error('Update config failed:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 获取当前配置
   * @returns {Promise<Object>}
   */
  async getConfig() {
    return await this.configService.load();
  }

  /**
   * 检查服务状态
   * @returns {Object}
   */
  getStatus() {
    return {
      initialized: this.initialized,
      hasCurrentTask: !!this.currentTask,
      currentTask: this.currentTask,
      coordinatorStatus: this.coordinator ? this.coordinator.getStatus() : 'not_initialized'
    };
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    if (!this.coordinator) {
      return { error: 'Coordinator not initialized' };
    }

    return this.coordinator.getStatistics();
  }

  /**
   * 从视频元素提取字幕
   * @param {HTMLVideoElement} video 视频元素
   * @returns {Promise<Array>} 字幕数组
   */
  async extractSubtitles(video) {
    return new Promise((resolve, reject) => {
      try {
        const textTracks = video.textTracks;
        let subtitles = [];

        // 查找可用的字幕轨道
        for (let i = 0; i < textTracks.length; i++) {
          const track = textTracks[i];
          
          if (track.kind === 'subtitles' || track.kind === 'captions') {
            track.mode = 'showing'; // 激活字幕轨道
            
            // 等待字幕加载
            const checkCues = () => {
              if (track.cues && track.cues.length > 0) {
                // 提取字幕数据
                for (let j = 0; j < track.cues.length; j++) {
                  const cue = track.cues[j];
                  subtitles.push({
                    startTime: cue.startTime,
                    endTime: cue.endTime,
                    text: cue.text
                  });
                }
                
                resolve(subtitles);
                return;
              }
              
              // 如果字幕还没加载，继续等待
              setTimeout(checkCues, 100);
            };
            
            checkCues();
            return;
          }
        }

        // 如果没有找到字幕轨道
        reject(new Error('No subtitle tracks found'));

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 销毁服务
   * @returns {Promise<void>}
   */
  async destroy() {
    try {
      if (this.currentTask) {
        await this.stopTranslation();
      }

      if (this.coordinator) {
        await this.coordinator.destroy();
        this.coordinator = null;
      }

      this.initialized = false;
      this.removeAllListeners();

    } catch (error) {
      console.error('Destroy service failed:', error);
      throw error;
    }
  }
}
