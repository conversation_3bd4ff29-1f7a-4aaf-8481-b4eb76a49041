/**
 * UI管理器
 * 负责管理用户界面和交互
 */

import { EventEmitter } from '../core/EventEmitter.js';
import { ControlPanel } from './ControlPanel.js';
import { StatusDisplay } from './StatusDisplay.js';
import { SettingsPanel } from './SettingsPanel.js';
import { NotificationManager } from './NotificationManager.js';

export class UIManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.chronoTranslate = options.chronoTranslate;
    this.logger = options.logger;
    
    this.components = {
      controlPanel: null,
      statusDisplay: null,
      settingsPanel: null,
      notificationManager: null
    };
    
    this.state = {
      visible: false,
      translationState: 'stopped', // 'stopped', 'starting', 'active'
      currentPlatform: null,
      settings: {
        targetLanguage: 'zh-CN',
        syncBuffer: 0.5,
        enableCache: true
      }
    };
    
    this.initialized = false;
  }

  /**
   * 初始化UI管理器
   */
  async init() {
    try {
      this.logger?.info('UIManager initializing...');
      
      // 等待DOM准备就绪
      await this.waitForDOM();
      
      // 初始化各个UI组件
      await this.initializeComponents();
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 检查是否应该显示UI
      this.checkShouldShowUI();
      
      this.initialized = true;
      this.logger?.info('UIManager initialized successfully');
      
    } catch (error) {
      this.logger?.error('Failed to initialize UIManager:', error);
      throw error;
    }
  }

  /**
   * 等待DOM准备就绪
   */
  async waitForDOM() {
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }
    
    // 等待视频元素出现
    await this.waitForVideoElement();
  }

  /**
   * 等待视频元素出现
   */
  async waitForVideoElement() {
    const maxWait = 10000; // 最多等待10秒
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWait) {
      const video = document.querySelector('video');
      if (video) {
        return video;
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    this.logger?.warn('Video element not found within timeout');
    return null;
  }

  /**
   * 初始化UI组件
   */
  async initializeComponents() {
    // 初始化通知管理器
    this.components.notificationManager = new NotificationManager();
    await this.components.notificationManager.init();
    
    // 初始化控制面板
    this.components.controlPanel = new ControlPanel({
      uiManager: this,
      logger: this.logger
    });
    await this.components.controlPanel.init();
    
    // 初始化状态显示
    this.components.statusDisplay = new StatusDisplay({
      uiManager: this,
      logger: this.logger
    });
    await this.components.statusDisplay.init();
    
    // 初始化设置面板
    this.components.settingsPanel = new SettingsPanel({
      uiManager: this,
      logger: this.logger
    });
    await this.components.settingsPanel.init();
    
    // 设置组件间的关联
    this.setupComponentRelations();
  }

  /**
   * 设置组件间的关联
   */
  setupComponentRelations() {
    // 控制面板事件
    this.components.controlPanel.on('startTranslation', (options) => {
      this.emit('startTranslation', options);
    });
    
    this.components.controlPanel.on('stopTranslation', () => {
      this.emit('stopTranslation');
    });
    
    this.components.controlPanel.on('showSettings', () => {
      this.components.settingsPanel.show();
    });
    
    // 设置面板事件
    this.components.settingsPanel.on('settingsChanged', (settings) => {
      this.state.settings = { ...this.state.settings, ...settings };
      this.emit('settingsChanged', this.state.settings);
    });
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听页面变化
    const observer = new MutationObserver(() => {
      this.checkShouldShowUI();
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  /**
   * 检查是否应该显示UI
   */
  checkShouldShowUI() {
    const video = document.querySelector('video');
    const shouldShow = !!video && this.isSupportedPage();
    
    if (shouldShow && !this.state.visible) {
      this.show();
    } else if (!shouldShow && this.state.visible) {
      this.hide();
    }
  }

  /**
   * 检查是否为支持的页面
   */
  isSupportedPage() {
    const url = window.location.href;
    return url.includes('youtube.com/watch') || 
           url.includes('tiktok.com') || 
           url.includes('bilibili.com');
  }

  /**
   * 显示UI
   */
  show() {
    if (this.state.visible) return;
    
    this.state.visible = true;
    
    // 显示各个组件
    Object.values(this.components).forEach(component => {
      if (component && component.show) {
        component.show();
      }
    });
    
    this.emit('uiShown');
    this.logger?.info('UI shown');
  }

  /**
   * 隐藏UI
   */
  hide() {
    if (!this.state.visible) return;
    
    this.state.visible = false;
    
    // 隐藏各个组件
    Object.values(this.components).forEach(component => {
      if (component && component.hide) {
        component.hide();
      }
    });
    
    this.emit('uiHidden');
    this.logger?.info('UI hidden');
  }

  /**
   * 更新翻译状态
   */
  updateTranslationState(state) {
    this.state.translationState = state;
    
    // 更新控制面板状态
    if (this.components.controlPanel) {
      this.components.controlPanel.updateTranslationState(state);
    }
    
    // 更新状态显示
    if (this.components.statusDisplay) {
      this.components.statusDisplay.updateTranslationState(state);
    }
    
    this.emit('translationStateChanged', state);
  }

  /**
   * 更新状态信息
   */
  updateStatus(message, type = 'info') {
    if (this.components.statusDisplay) {
      this.components.statusDisplay.updateStatus(message, type);
    }
    
    this.emit('statusUpdated', { message, type });
  }

  /**
   * 显示平台信息
   */
  showPlatformInfo(platform) {
    this.state.currentPlatform = platform;
    
    if (this.components.controlPanel) {
      this.components.controlPanel.updatePlatformInfo(platform);
    }
    
    this.emit('platformInfoUpdated', platform);
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    if (this.components.notificationManager) {
      this.components.notificationManager.showError(message);
    }
    
    this.updateStatus(message, 'error');
    this.emit('errorShown', message);
  }

  /**
   * 显示成功信息
   */
  showSuccess(message) {
    if (this.components.notificationManager) {
      this.components.notificationManager.showSuccess(message);
    }
    
    this.emit('successShown', message);
  }

  /**
   * 显示警告信息
   */
  showWarning(message) {
    if (this.components.notificationManager) {
      this.components.notificationManager.showWarning(message);
    }
    
    this.emit('warningShown', message);
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    Object.values(this.components).forEach(component => {
      if (component && component.handleResize) {
        component.handleResize();
      }
    });
  }

  /**
   * 获取UI状态
   */
  getState() {
    return {
      ...this.state,
      components: Object.keys(this.components).reduce((acc, key) => {
        const component = this.components[key];
        acc[key] = component && component.getState ? component.getState() : null;
        return acc;
      }, {})
    };
  }

  /**
   * 更新设置
   */
  updateSettings(settings) {
    this.state.settings = { ...this.state.settings, ...settings };
    
    // 更新设置面板
    if (this.components.settingsPanel) {
      this.components.settingsPanel.updateSettings(this.state.settings);
    }
    
    this.emit('settingsUpdated', this.state.settings);
  }

  /**
   * 销毁UI管理器
   */
  async destroy() {
    this.logger?.info('Destroying UIManager...');
    
    // 销毁所有组件
    for (const component of Object.values(this.components)) {
      if (component && component.destroy) {
        await component.destroy();
      }
    }
    
    this.components = {};
    this.initialized = false;
    this.removeAllListeners();
    
    this.logger?.info('UIManager destroyed');
  }
}
