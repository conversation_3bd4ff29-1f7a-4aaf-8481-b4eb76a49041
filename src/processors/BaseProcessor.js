/**
 * 基础处理器实现
 * 提供所有处理器的通用功能
 */

import { IProcessor, IBatchProcessor } from '../core/interfaces/IProcessor.js';
import { EventEmitter } from '../core/EventEmitter.js';

export class BaseProcessor extends IProcessor {
  constructor(name, config = {}) {
    super();
    this.name = name;
    this.config = {
      timeout: 30000, // 30秒超时
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
    
    // 统计信息
    this.stats = {
      totalProcessed: 0,
      totalFailed: 0,
      totalTime: 0,
      averageTime: 0,
      lastProcessedAt: null
    };
  }

  /**
   * 获取处理器名称
   * @returns {string}
   */
  getName() {
    return this.name;
  }

  /**
   * 检查处理器是否可用
   * @returns {boolean}
   */
  isAvailable() {
    return true; // 子类可以重写此方法
  }

  /**
   * 获取处理器配置
   * @returns {Object}
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 更新处理器配置
   * @param {Object} config 新配置
   */
  updateConfig(config) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 验证输入数据
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {boolean}
   */
  validateInput(input, options = {}) {
    return input !== null && input !== undefined;
  }

  /**
   * 预处理输入数据
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {any} 预处理后的数据
   */
  preprocessInput(input, options = {}) {
    return input;
  }

  /**
   * 后处理输出数据
   * @param {any} output 输出数据
   * @param {Object} options 选项
   * @returns {any} 后处理后的数据
   */
  postprocessOutput(output, options = {}) {
    return output;
  }

  /**
   * 处理错误
   * @param {Error} error 错误对象
   * @param {any} input 输入数据
   * @param {Object} options 选项
   * @returns {any} 错误处理结果
   */
  handleError(error, input, options = {}) {
    console.error(`${this.name} processor error:`, error);
    this.stats.totalFailed++;
    throw error;
  }

  /**
   * 核心处理方法（子类必须实现）
   * @param {any} input 输入数据
   * @param {Object} options 处理选项
   * @returns {Promise<any>} 处理结果
   */
  async processCore(input, options = {}) {
    throw new Error(`processCore method must be implemented in ${this.name}`);
  }

  /**
   * 处理数据的主方法
   * @param {any} input 输入数据
   * @param {Object} options 处理选项
   * @returns {Promise<any>} 处理结果
   */
  async process(input, options = {}) {
    const startTime = Date.now();
    
    try {
      // 验证输入
      if (!this.validateInput(input, options)) {
        throw new Error('Invalid input data');
      }

      // 检查处理器是否可用
      if (!this.isAvailable()) {
        throw new Error(`${this.name} processor is not available`);
      }

      // 预处理
      const preprocessedInput = this.preprocessInput(input, options);

      // 核心处理（带超时）
      const result = await this.withTimeout(
        this.processCore(preprocessedInput, options),
        this.config.timeout
      );

      // 后处理
      const finalResult = this.postprocessOutput(result, options);

      // 更新统计信息
      this.updateStats(startTime);

      return finalResult;

    } catch (error) {
      return this.handleError(error, input, options);
    }
  }

  /**
   * 带超时的处理
   * @param {Promise} promise 要执行的Promise
   * @param {number} timeout 超时时间（毫秒）
   * @returns {Promise}
   */
  async withTimeout(promise, timeout) {
    return Promise.race([
      promise,
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`${this.name} processor timeout after ${timeout}ms`));
        }, timeout);
      })
    ]);
  }

  /**
   * 更新统计信息
   * @param {number} startTime 开始时间
   */
  updateStats(startTime) {
    const processingTime = Date.now() - startTime;
    this.stats.totalProcessed++;
    this.stats.totalTime += processingTime;
    this.stats.averageTime = this.stats.totalTime / this.stats.totalProcessed;
    this.stats.lastProcessedAt = new Date();
  }

  /**
   * 获取处理器统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      name: this.name,
      available: this.isAvailable(),
      config: this.getConfig(),
      stats: { ...this.stats }
    };
  }

  /**
   * 重置统计信息
   */
  resetStatistics() {
    this.stats = {
      totalProcessed: 0,
      totalFailed: 0,
      totalTime: 0,
      averageTime: 0,
      lastProcessedAt: null
    };
  }
}

/**
 * 批处理器基类
 */
export class BaseBatchProcessor extends BaseProcessor {
  constructor(name, config = {}) {
    super(name, config);
    this.config.batchSize = config.batchSize || 10;
  }

  /**
   * 获取最佳批处理大小
   * @returns {number}
   */
  getOptimalBatchSize() {
    return this.config.batchSize;
  }

  /**
   * 检查是否支持批处理
   * @returns {boolean}
   */
  supportsBatch() {
    return true;
  }

  /**
   * 批量处理核心方法（子类必须实现）
   * @param {Array} inputs 输入数据数组
   * @param {Object} options 处理选项
   * @returns {Promise<Array>} 处理结果数组
   */
  async batchProcessCore(inputs, options = {}) {
    throw new Error(`batchProcessCore method must be implemented in ${this.name}`);
  }

  /**
   * 批量处理数据
   * @param {Array} inputs 输入数据数组
   * @param {Object} options 处理选项
   * @returns {Promise<Array>} 处理结果数组
   */
  async batchProcess(inputs, options = {}) {
    if (!Array.isArray(inputs)) {
      throw new Error('Inputs must be an array for batch processing');
    }

    if (inputs.length === 0) {
      return [];
    }

    const startTime = Date.now();

    try {
      // 验证所有输入
      for (let i = 0; i < inputs.length; i++) {
        if (!this.validateInput(inputs[i], options)) {
          throw new Error(`Invalid input data at index ${i}`);
        }
      }

      // 检查处理器是否可用
      if (!this.isAvailable()) {
        throw new Error(`${this.name} processor is not available`);
      }

      // 预处理所有输入
      const preprocessedInputs = inputs.map(input => 
        this.preprocessInput(input, options)
      );

      // 批量核心处理（带超时）
      const results = await this.withTimeout(
        this.batchProcessCore(preprocessedInputs, options),
        this.config.timeout * inputs.length // 批处理超时时间按数量放大
      );

      // 后处理所有结果
      const finalResults = results.map(result => 
        this.postprocessOutput(result, options)
      );

      // 更新统计信息
      this.updateBatchStats(startTime, inputs.length);

      return finalResults;

    } catch (error) {
      this.stats.totalFailed += inputs.length;
      throw error;
    }
  }

  /**
   * 更新批处理统计信息
   * @param {number} startTime 开始时间
   * @param {number} batchSize 批处理大小
   */
  updateBatchStats(startTime, batchSize) {
    const processingTime = Date.now() - startTime;
    this.stats.totalProcessed += batchSize;
    this.stats.totalTime += processingTime;
    this.stats.averageTime = this.stats.totalTime / this.stats.totalProcessed;
    this.stats.lastProcessedAt = new Date();
  }

  /**
   * 单个处理（通过批处理实现）
   * @param {any} input 输入数据
   * @param {Object} options 处理选项
   * @returns {Promise<any>} 处理结果
   */
  async process(input, options = {}) {
    const results = await this.batchProcess([input], options);
    return results[0];
  }
}
