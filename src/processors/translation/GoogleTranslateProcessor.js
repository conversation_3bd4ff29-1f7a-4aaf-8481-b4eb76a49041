/**
 * Google翻译处理器
 * 纯粹的翻译处理逻辑，无状态管理
 */

import { BaseBatchProcessor } from '../BaseProcessor.js';

export class GoogleTranslateProcessor extends BaseBatchProcessor {
  constructor(config = {}) {
    super('GoogleTranslate', {
      batchSize: 50, // Google翻译支持较大批次
      timeout: 30000,
      apiKey: config.apiKey || '',
      endpoint: config.endpoint || 'https://translation.googleapis.com/language/translate/v2',
      ...config
    });

    // 速率限制
    this.rateLimit = {
      requests: 100,
      window: 60000, // 1分钟
      current: 0,
      resetTime: Date.now() + 60000
    };
  }

  /**
   * 检查处理器是否可用
   * @returns {boolean}
   */
  isAvailable() {
    return !!this.config.apiKey;
  }

  /**
   * 验证输入数据
   * @param {string|Object} input 输入数据
   * @param {Object} options 选项
   * @returns {boolean}
   */
  validateInput(input, options = {}) {
    if (typeof input === 'string') {
      return input.trim().length > 0;
    }
    
    if (typeof input === 'object' && input.text) {
      return input.text.trim().length > 0;
    }
    
    return false;
  }

  /**
   * 预处理输入数据
   * @param {string|Object} input 输入数据
   * @param {Object} options 选项
   * @returns {string} 预处理后的文本
   */
  preprocessInput(input, options = {}) {
    let text = typeof input === 'string' ? input : input.text;
    
    // 清理文本
    text = text.trim();
    
    // 移除多余的空白字符
    text = text.replace(/\s+/g, ' ');
    
    // 处理特殊字符
    text = text.replace(/&nbsp;/g, ' ');
    
    return text;
  }

  /**
   * 后处理输出数据
   * @param {string} output 输出数据
   * @param {Object} options 选项
   * @returns {string} 后处理后的文本
   */
  postprocessOutput(output, options = {}) {
    if (!output) return '';
    
    // 清理翻译结果
    let result = output.trim();
    
    // 修复常见的翻译问题
    result = result.replace(/\s+/g, ' ');
    
    return result;
  }

  /**
   * 检查速率限制
   * @returns {boolean}
   */
  checkRateLimit() {
    const now = Date.now();
    
    // 重置速率限制窗口
    if (now >= this.rateLimit.resetTime) {
      this.rateLimit.current = 0;
      this.rateLimit.resetTime = now + this.rateLimit.window;
    }
    
    // 检查是否超过限制
    if (this.rateLimit.current >= this.rateLimit.requests) {
      return false;
    }
    
    this.rateLimit.current++;
    return true;
  }

  /**
   * 等待速率限制重置
   * @returns {Promise<void>}
   */
  async waitForRateLimit() {
    const waitTime = this.rateLimit.resetTime - Date.now();
    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * 批量翻译核心处理
   * @param {Array<string>} texts 文本数组
   * @param {Object} options 处理选项
   * @returns {Promise<Array<string>>} 翻译结果数组
   */
  async batchProcessCore(texts, options = {}) {
    if (!texts || texts.length === 0) {
      return [];
    }

    // 检查速率限制
    if (!this.checkRateLimit()) {
      await this.waitForRateLimit();
    }

    const {
      targetLanguage = 'zh-CN',
      sourceLanguage = 'auto'
    } = options;

    try {
      const response = await this.makeRequest({
        q: texts,
        target: targetLanguage,
        source: sourceLanguage,
        format: 'text'
      });

      if (!response.data || !response.data.translations) {
        throw new Error('Invalid response from Google Translate API');
      }

      return response.data.translations.map(translation => 
        translation.translatedText || ''
      );

    } catch (error) {
      console.error('Google Translate API error:', error);
      throw new Error(`Translation failed: ${error.message}`);
    }
  }

  /**
   * 发送API请求
   * @param {Object} data 请求数据
   * @returns {Promise<Object>} 响应数据
   */
  async makeRequest(data) {
    const url = `${this.config.endpoint}?key=${this.config.apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ChronoTranslate/3.0'
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * 检测文本语言
   * @param {string} text 要检测的文本
   * @returns {Promise<string>} 语言代码
   */
  async detectLanguage(text) {
    if (!text || !text.trim()) {
      return 'auto';
    }

    try {
      const url = `https://translation.googleapis.com/language/translate/v2/detect?key=${this.config.apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          q: text
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.data && result.data.detections && result.data.detections[0]) {
        return result.data.detections[0][0].language;
      }

      return 'auto';

    } catch (error) {
      console.error('Language detection error:', error);
      return 'auto';
    }
  }

  /**
   * 获取支持的语言列表
   * @returns {Promise<Array>} 语言列表
   */
  async getSupportedLanguages() {
    try {
      const url = `https://translation.googleapis.com/language/translate/v2/languages?key=${this.config.apiKey}&target=en`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.data && result.data.languages) {
        return result.data.languages.map(lang => ({
          code: lang.language,
          name: lang.name
        }));
      }

      return this.getDefaultLanguages();

    } catch (error) {
      console.error('Get supported languages error:', error);
      return this.getDefaultLanguages();
    }
  }

  /**
   * 获取默认语言列表
   * @returns {Array}
   */
  getDefaultLanguages() {
    return [
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'zh-TW', name: '中文 (繁體)' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' },
      { code: 'ko', name: '한국어' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'ru', name: 'Русский' },
      { code: 'pt', name: 'Português' },
      { code: 'it', name: 'Italiano' },
      { code: 'ar', name: 'العربية' },
      { code: 'hi', name: 'हिन्दी' },
      { code: 'th', name: 'ไทย' },
      { code: 'vi', name: 'Tiếng Việt' }
    ];
  }

  /**
   * 获取处理器统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      ...super.getStatistics(),
      rateLimit: {
        current: this.rateLimit.current,
        limit: this.rateLimit.requests,
        resetTime: this.rateLimit.resetTime
      }
    };
  }
}
