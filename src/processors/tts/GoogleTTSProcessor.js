/**
 * Google TTS处理器
 * 纯粹的文本转语音处理逻辑，无状态管理
 */

import { BaseBatchProcessor } from '../BaseProcessor.js';

export class GoogleTTSProcessor extends BaseBatchProcessor {
  constructor(config = {}) {
    super('GoogleTTS', {
      batchSize: 20, // TTS批处理大小相对较小
      timeout: 60000, // TTS需要更长的超时时间
      apiKey: config.apiKey || '',
      endpoint: config.endpoint || 'https://texttospeech.googleapis.com/v1/text:synthesize',
      ...config
    });

    // 默认语音配置
    this.defaultVoiceConfig = {
      languageCode: 'zh-CN',
      name: 'zh-CN-Wavenet-A',
      ssmlGender: 'FEMALE'
    };

    // 音频配置
    this.defaultAudioConfig = {
      audioEncoding: 'MP3',
      speakingRate: 1.0,
      pitch: 0.0,
      volumeGainDb: 0.0
    };

    // 速率限制
    this.rateLimit = {
      requests: 60,
      window: 60000, // 1分钟
      current: 0,
      resetTime: Date.now() + 60000
    };
  }

  /**
   * 检查处理器是否可用
   * @returns {boolean}
   */
  isAvailable() {
    return !!this.config.apiKey;
  }

  /**
   * 验证输入数据
   * @param {string|Object} input 输入数据
   * @param {Object} options 选项
   * @returns {boolean}
   */
  validateInput(input, options = {}) {
    let text = typeof input === 'string' ? input : input?.text;
    
    if (!text || typeof text !== 'string') {
      return false;
    }

    // 检查文本长度（Google TTS限制）
    if (text.length > 5000) {
      return false;
    }

    return text.trim().length > 0;
  }

  /**
   * 预处理输入数据
   * @param {string|Object} input 输入数据
   * @param {Object} options 选项
   * @returns {Object} 预处理后的数据
   */
  preprocessInput(input, options = {}) {
    let text = typeof input === 'string' ? input : input.text;
    let metadata = typeof input === 'object' ? input : {};
    
    // 清理文本
    text = text.trim();
    
    // 移除HTML标签
    text = text.replace(/<[^>]*>/g, '');
    
    // 处理特殊字符
    text = text.replace(/&nbsp;/g, ' ');
    text = text.replace(/&amp;/g, '&');
    text = text.replace(/&lt;/g, '<');
    text = text.replace(/&gt;/g, '>');
    
    // 移除多余的空白字符
    text = text.replace(/\s+/g, ' ');
    
    // 限制文本长度
    if (text.length > 5000) {
      text = text.substring(0, 4997) + '...';
    }

    return {
      text,
      ...metadata
    };
  }

  /**
   * 后处理输出数据
   * @param {string} audioContent Base64编码的音频数据
   * @param {Object} options 选项
   * @returns {string} 音频URL
   */
  postprocessOutput(audioContent, options = {}) {
    if (!audioContent) {
      throw new Error('No audio content received');
    }

    try {
      // 将Base64音频数据转换为Blob URL
      const audioBytes = atob(audioContent);
      const audioArray = new Uint8Array(audioBytes.length);
      
      for (let i = 0; i < audioBytes.length; i++) {
        audioArray[i] = audioBytes.charCodeAt(i);
      }

      const audioBlob = new Blob([audioArray], { type: 'audio/mp3' });
      const audioUrl = URL.createObjectURL(audioBlob);

      return audioUrl;

    } catch (error) {
      console.error('Error processing audio content:', error);
      throw new Error('Failed to process audio content');
    }
  }

  /**
   * 检查速率限制
   * @returns {boolean}
   */
  checkRateLimit() {
    const now = Date.now();
    
    // 重置速率限制窗口
    if (now >= this.rateLimit.resetTime) {
      this.rateLimit.current = 0;
      this.rateLimit.resetTime = now + this.rateLimit.window;
    }
    
    // 检查是否超过限制
    if (this.rateLimit.current >= this.rateLimit.requests) {
      return false;
    }
    
    this.rateLimit.current++;
    return true;
  }

  /**
   * 等待速率限制重置
   * @returns {Promise<void>}
   */
  async waitForRateLimit() {
    const waitTime = this.rateLimit.resetTime - Date.now();
    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * 批量TTS核心处理
   * @param {Array<Object>} inputs 输入数据数组
   * @param {Object} options 处理选项
   * @returns {Promise<Array<string>>} 音频URL数组
   */
  async batchProcessCore(inputs, options = {}) {
    if (!inputs || inputs.length === 0) {
      return [];
    }

    const results = [];
    
    // TTS通常需要逐个处理，因为每个请求都比较大
    for (const input of inputs) {
      // 检查速率限制
      if (!this.checkRateLimit()) {
        await this.waitForRateLimit();
      }

      try {
        const audioUrl = await this.synthesizeSingle(input, options);
        results.push(audioUrl);
      } catch (error) {
        console.error('TTS synthesis error:', error);
        results.push(null); // 失败时返回null
      }
    }

    return results;
  }

  /**
   * 合成单个文本
   * @param {Object} input 输入数据
   * @param {Object} options 选项
   * @returns {Promise<string>} 音频URL
   */
  async synthesizeSingle(input, options = {}) {
    const {
      languageCode = 'zh-CN',
      voiceName = 'zh-CN-Wavenet-A',
      ssmlGender = 'FEMALE',
      speakingRate = 1.0,
      pitch = 0.0,
      volumeGainDb = 0.0
    } = options;

    const requestBody = {
      input: {
        text: input.text
      },
      voice: {
        languageCode,
        name: voiceName,
        ssmlGender
      },
      audioConfig: {
        audioEncoding: 'MP3',
        speakingRate,
        pitch,
        volumeGainDb
      }
    };

    try {
      const response = await this.makeRequest(requestBody);
      
      if (!response.audioContent) {
        throw new Error('No audio content in response');
      }

      return this.postprocessOutput(response.audioContent, options);

    } catch (error) {
      console.error('Google TTS API error:', error);
      throw new Error(`TTS synthesis failed: ${error.message}`);
    }
  }

  /**
   * 发送API请求
   * @param {Object} data 请求数据
   * @returns {Promise<Object>} 响应数据
   */
  async makeRequest(data) {
    const url = `${this.config.endpoint}?key=${this.config.apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ChronoTranslate/3.0'
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    return await response.json();
  }

  /**
   * 获取可用语音列表
   * @param {string} languageCode 语言代码
   * @returns {Promise<Array>} 语音列表
   */
  async getAvailableVoices(languageCode = 'zh-CN') {
    try {
      const url = `https://texttospeech.googleapis.com/v1/voices?key=${this.config.apiKey}&languageCode=${languageCode}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.voices) {
        return result.voices.map(voice => ({
          name: voice.name,
          languageCode: voice.languageCodes[0],
          ssmlGender: voice.ssmlGender,
          naturalSampleRateHertz: voice.naturalSampleRateHertz
        }));
      }

      return this.getDefaultVoices(languageCode);

    } catch (error) {
      console.error('Get available voices error:', error);
      return this.getDefaultVoices(languageCode);
    }
  }

  /**
   * 获取默认语音列表
   * @param {string} languageCode 语言代码
   * @returns {Array}
   */
  getDefaultVoices(languageCode = 'zh-CN') {
    const voiceMap = {
      'zh-CN': [
        { name: 'zh-CN-Wavenet-A', ssmlGender: 'FEMALE' },
        { name: 'zh-CN-Wavenet-B', ssmlGender: 'MALE' },
        { name: 'zh-CN-Wavenet-C', ssmlGender: 'MALE' },
        { name: 'zh-CN-Wavenet-D', ssmlGender: 'FEMALE' }
      ],
      'en-US': [
        { name: 'en-US-Wavenet-A', ssmlGender: 'MALE' },
        { name: 'en-US-Wavenet-B', ssmlGender: 'MALE' },
        { name: 'en-US-Wavenet-C', ssmlGender: 'FEMALE' },
        { name: 'en-US-Wavenet-D', ssmlGender: 'MALE' }
      ],
      'ja-JP': [
        { name: 'ja-JP-Wavenet-A', ssmlGender: 'FEMALE' },
        { name: 'ja-JP-Wavenet-B', ssmlGender: 'FEMALE' },
        { name: 'ja-JP-Wavenet-C', ssmlGender: 'MALE' },
        { name: 'ja-JP-Wavenet-D', ssmlGender: 'MALE' }
      ]
    };

    return voiceMap[languageCode] || voiceMap['zh-CN'];
  }

  /**
   * 获取处理器统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      ...super.getStatistics(),
      rateLimit: {
        current: this.rateLimit.current,
        limit: this.rateLimit.requests,
        resetTime: this.rateLimit.resetTime
      }
    };
  }
}
