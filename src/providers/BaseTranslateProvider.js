/**
 * 翻译提供商基类
 * 定义所有翻译提供商必须实现的接口
 */

import { EventEmitter } from '../core/EventEmitter.js';

export class BaseTranslateProvider extends EventEmitter {
  constructor() {
    super();
    this.name = '';
    this.apiKey = '';
    this.rateLimit = {
      requests: 100,
      window: 60000, // 1分钟
      current: 0,
      resetTime: Date.now() + 60000
    };
  }

  /**
   * 设置API密钥
   * @param {string} apiKey API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  /**
   * 检查是否可用
   * @returns {boolean}
   */
  isAvailable() {
    return !!this.apiKey;
  }

  /**
   * 批量翻译文本
   * @param {Array<string>} texts 要翻译的文本数组
   * @param {string} targetLanguage 目标语言
   * @param {Object} options 选项
   * @returns {Promise<Array<string>>}
   */
  async batchTranslate(texts, targetLanguage, options = {}) {
    throw new Error('batchTranslate method must be implemented');
  }

  /**
   * 翻译单个文本
   * @param {string} text 要翻译的文本
   * @param {string} targetLanguage 目标语言
   * @param {Object} options 选项
   * @returns {Promise<string>}
   */
  async translateText(text, targetLanguage, options = {}) {
    const results = await this.batchTranslate([text], targetLanguage, options);
    return results[0] || text;
  }

  /**
   * 检测文本语言
   * @param {string} text 要检测的文本
   * @returns {Promise<string>}
   */
  async detectLanguage(text) {
    // 子类可以重写此方法
    return 'auto';
  }

  /**
   * 获取支持的语言列表
   * @returns {Promise<Array>}
   */
  async getSupportedLanguages() {
    // 返回通用语言列表，子类可以重写
    return [
      { code: 'zh-CN', name: '中文 (简体)' },
      { code: 'zh-TW', name: '中文 (繁體)' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' },
      { code: 'ko', name: '한국어' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'ru', name: 'Русский' }
    ];
  }

  /**
   * 检查速率限制
   * @returns {boolean}
   */
  checkRateLimit() {
    const now = Date.now();
    
    // 重置速率限制窗口
    if (now >= this.rateLimit.resetTime) {
      this.rateLimit.current = 0;
      this.rateLimit.resetTime = now + this.rateLimit.window;
    }
    
    // 检查是否超过限制
    if (this.rateLimit.current >= this.rateLimit.requests) {
      return false;
    }
    
    this.rateLimit.current++;
    return true;
  }

  /**
   * 等待速率限制重置
   * @returns {Promise<void>}
   */
  async waitForRateLimit() {
    const waitTime = this.rateLimit.resetTime - Date.now();
    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @param {Object} context 上下文信息
   */
  handleApiError(error, context = {}) {
    const errorInfo = {
      provider: this.name,
      error: error.message,
      context
    };

    // 根据错误类型进行不同处理
    if (error.message.includes('rate limit') || error.message.includes('429')) {
      errorInfo.type = 'rate_limit';
      this.emit('rateLimitExceeded', errorInfo);
    } else if (error.message.includes('unauthorized') || error.message.includes('401')) {
      errorInfo.type = 'auth_error';
      this.emit('authError', errorInfo);
    } else if (error.message.includes('quota') || error.message.includes('billing')) {
      errorInfo.type = 'quota_error';
      this.emit('quotaError', errorInfo);
    } else {
      errorInfo.type = 'api_error';
    }

    this.emit('error', errorInfo);
    throw error;
  }

  /**
   * 发送HTTP请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Response>}
   */
  async makeRequest(url, options = {}) {
    // 检查速率限制
    if (!this.checkRateLimit()) {
      await this.waitForRateLimit();
    }

    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ChronoTranslate/1.0'
      }
    };

    const requestOptions = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, requestOptions);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      return response;
    } catch (error) {
      this.handleApiError(error, { url, options: requestOptions });
    }
  }

  /**
   * 清理文本
   * @param {string} text 要清理的文本
   * @returns {string}
   */
  cleanText(text) {
    if (!text || typeof text !== 'string') {
      return '';
    }

    return text
      .trim()
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n+/g, '\n') // 合并多个换行
      .replace(/[^\S\n]+/g, ' '); // 清理其他空白字符
  }

  /**
   * 分割长文本
   * @param {string} text 要分割的文本
   * @param {number} maxLength 最大长度
   * @returns {Array<string>}
   */
  splitText(text, maxLength = 5000) {
    if (text.length <= maxLength) {
      return [text];
    }

    const chunks = [];
    const sentences = text.split(/[.!?。！？]/);
    let currentChunk = '';

    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (!trimmedSentence) continue;

      const potentialChunk = currentChunk + 
        (currentChunk ? '. ' : '') + trimmedSentence;

      if (potentialChunk.length <= maxLength) {
        currentChunk = potentialChunk;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk);
        }
        currentChunk = trimmedSentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  /**
   * 重试机制
   * @param {Function} fn 要重试的函数
   * @param {number} maxRetries 最大重试次数
   * @param {number} delay 重试延迟
   * @returns {Promise<any>}
   */
  async retry(fn, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i === maxRetries) {
          break;
        }

        // 指数退避
        const waitTime = delay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    throw lastError;
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      name: this.name,
      requestCount: this.rateLimit.current,
      rateLimit: this.rateLimit.requests,
      resetTime: this.rateLimit.resetTime
    };
  }

  /**
   * 销毁提供商实例
   */
  async destroy() {
    this.removeAllListeners();
  }
}
