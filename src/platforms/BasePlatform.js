/**
 * 平台基类
 * 定义所有平台必须实现的接口
 */

import { EventEmitter } from '../core/EventEmitter.js';

export class BasePlatform extends EventEmitter {
  constructor() {
    super();
    this.name = '';
    this.videoElement = null;
    this.initialized = false;
  }

  /**
   * 检查是否支持当前URL
   * @param {string} url 页面URL
   * @returns {Promise<boolean>}
   */
  async isSupported(url) {
    throw new Error('isSupported method must be implemented');
  }

  /**
   * 初始化平台
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    await this.findVideoElement();
    await this.setupPlatformSpecificFeatures();
    
    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 查找视频元素
   * @returns {Promise<HTMLVideoElement|null>}
   */
  async findVideoElement() {
    const video = document.querySelector('video');
    if (video) {
      this.videoElement = video;
      this.emit('videoDetected', {
        element: video,
        src: video.src || video.currentSrc
      });
      return video;
    }
    return null;
  }

  /**
   * 设置平台特定功能
   * @returns {Promise<void>}
   */
  async setupPlatformSpecificFeatures() {
    // 子类可以重写此方法
  }

  /**
   * 获取视频元素
   * @returns {HTMLVideoElement|null}
   */
  getVideoElement() {
    return this.videoElement;
  }

  /**
   * 获取视频ID
   * @returns {string|null}
   */
  getVideoId() {
    throw new Error('getVideoId method must be implemented');
  }

  /**
   * 检查是否有字幕
   * @returns {Promise<boolean>}
   */
  async hasSubtitles() {
    if (!this.videoElement) {
      return false;
    }

    const textTracks = this.videoElement.textTracks;
    for (let i = 0; i < textTracks.length; i++) {
      const track = textTracks[i];
      if ((track.kind === 'subtitles' || track.kind === 'captions') && 
          track.cues && track.cues.length > 0) {
        return true;
      }
    }

    return false;
  }

  /**
   * 提取字幕
   * @returns {Promise<Array>}
   */
  async extractSubtitles() {
    const subtitles = [];

    if (!this.videoElement) {
      return subtitles;
    }

    // 首先尝试从HTML5 textTracks提取
    const htmlSubtitles = await this.extractHtmlSubtitles();
    if (htmlSubtitles.length > 0) {
      return htmlSubtitles;
    }

    // 然后尝试平台特定的字幕提取
    const platformSubtitles = await this.extractPlatformSubtitles();
    if (platformSubtitles.length > 0) {
      return platformSubtitles;
    }

    return subtitles;
  }

  /**
   * 从HTML5 textTracks提取字幕
   * @returns {Promise<Array>}
   */
  async extractHtmlSubtitles() {
    const subtitles = [];

    if (!this.videoElement || !this.videoElement.textTracks) {
      return subtitles;
    }

    for (let i = 0; i < this.videoElement.textTracks.length; i++) {
      const track = this.videoElement.textTracks[i];
      
      if ((track.kind === 'subtitles' || track.kind === 'captions') && 
          track.cues && track.cues.length > 0) {
        
        for (let j = 0; j < track.cues.length; j++) {
          const cue = track.cues[j];
          subtitles.push({
            id: `${i}-${j}`,
            text: cue.text,
            startTime: cue.startTime,
            endTime: cue.endTime
          });
        }
        break; // 只处理第一个可用的字幕轨道
      }
    }

    return subtitles;
  }

  /**
   * 提取平台特定字幕
   * @returns {Promise<Array>}
   */
  async extractPlatformSubtitles() {
    // 子类可以重写此方法实现平台特定的字幕提取
    return [];
  }

  /**
   * 获取视频信息
   * @returns {Object}
   */
  getVideoInfo() {
    if (!this.videoElement) {
      return null;
    }

    return {
      id: this.getVideoId(),
      title: this.getVideoTitle(),
      duration: this.videoElement.duration,
      currentTime: this.videoElement.currentTime,
      src: this.videoElement.src || this.videoElement.currentSrc,
      platform: this.name
    };
  }

  /**
   * 获取视频标题
   * @returns {string}
   */
  getVideoTitle() {
    // 默认从页面标题获取
    return document.title;
  }

  /**
   * 静音视频
   */
  muteVideo() {
    if (this.videoElement) {
      this.videoElement.muted = true;
    }
  }

  /**
   * 取消静音
   */
  unmuteVideo() {
    if (this.videoElement) {
      this.videoElement.muted = false;
    }
  }

  /**
   * 获取当前播放时间
   * @returns {number}
   */
  getCurrentTime() {
    return this.videoElement ? this.videoElement.currentTime : 0;
  }

  /**
   * 设置播放时间
   * @param {number} time 时间（秒）
   */
  setCurrentTime(time) {
    if (this.videoElement) {
      this.videoElement.currentTime = time;
    }
  }

  /**
   * 播放视频
   */
  play() {
    if (this.videoElement) {
      this.videoElement.play();
    }
  }

  /**
   * 暂停视频
   */
  pause() {
    if (this.videoElement) {
      this.videoElement.pause();
    }
  }

  /**
   * 检查视频是否正在播放
   * @returns {boolean}
   */
  isPlaying() {
    if (!this.videoElement) {
      return false;
    }
    return !this.videoElement.paused && !this.videoElement.ended;
  }

  /**
   * 添加时间更新监听器
   * @param {Function} callback 回调函数
   */
  onTimeUpdate(callback) {
    if (this.videoElement) {
      this.videoElement.addEventListener('timeupdate', callback);
    }
  }

  /**
   * 移除时间更新监听器
   * @param {Function} callback 回调函数
   */
  offTimeUpdate(callback) {
    if (this.videoElement) {
      this.videoElement.removeEventListener('timeupdate', callback);
    }
  }

  /**
   * 销毁平台实例
   */
  async destroy() {
    this.videoElement = null;
    this.initialized = false;
    this.removeAllListeners();
  }
}
