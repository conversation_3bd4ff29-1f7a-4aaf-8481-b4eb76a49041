/**
 * TikTok平台实现
 */

import { BasePlatform } from './BasePlatform.js';

export class TikTokPlatform extends BasePlatform {
  constructor() {
    super();
    this.name = 'tiktok';
    this.videoData = null;
  }

  /**
   * 检查是否支持当前URL
   */
  async isSupported(url) {
    return url.includes('tiktok.com') || url.includes('douyin.com');
  }

  /**
   * 查找视频元素
   */
  async findVideoElement() {
    // TikTok的视频元素可能在不同的容器中
    const selectors = [
      'video',
      '[data-e2e="video-player"] video',
      '.video-player video',
      '.tiktok-video video'
    ];

    for (const selector of selectors) {
      const video = document.querySelector(selector);
      if (video) {
        this.videoElement = video;
        this.emit('videoDetected', {
          element: video,
          src: video.src || video.currentSrc
        });
        return video;
      }
    }

    return null;
  }

  /**
   * 获取视频ID
   */
  getVideoId() {
    // 从URL中提取视频ID
    const match = window.location.href.match(/\/video\/(\d+)/);
    if (match) {
      return match[1];
    }

    // 尝试从页面数据中获取
    try {
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        const content = script.textContent;
        if (content.includes('videoId') || content.includes('aweme_id')) {
          const videoIdMatch = content.match(/"(?:videoId|aweme_id)":"([^"]+)"/);
          if (videoIdMatch) {
            return videoIdMatch[1];
          }
        }
      }
    } catch (error) {
      console.error('Error extracting TikTok video ID:', error);
    }

    return null;
  }

  /**
   * 获取视频标题
   */
  getVideoTitle() {
    // TikTok视频描述选择器
    const titleSelectors = [
      '[data-e2e="video-desc"]',
      '.video-meta-caption',
      '.tt-video-meta-caption',
      '.video-description'
    ];

    for (const selector of titleSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return document.title.replace(' | TikTok', '');
  }

  /**
   * 设置平台特定功能
   */
  async setupPlatformSpecificFeatures() {
    // 监听TikTok的视频切换
    this.setupVideoNavigation();
    
    // 提取视频数据
    this.extractVideoData();
    
    // 设置自动播放检测
    this.setupAutoplayDetection();
  }

  /**
   * 设置视频导航监听
   */
  setupVideoNavigation() {
    let lastVideoId = this.getVideoId();
    
    const observer = new MutationObserver(() => {
      const currentVideoId = this.getVideoId();
      if (currentVideoId && currentVideoId !== lastVideoId) {
        lastVideoId = currentVideoId;
        
        // 视频切换，重新查找视频元素
        setTimeout(() => {
          this.findVideoElement();
          this.extractVideoData();
          this.emit('videoChanged', { videoId: currentVideoId });
        }, 500);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 提取视频数据
   */
  extractVideoData() {
    try {
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        const content = script.textContent;
        
        // 查找包含视频数据的script标签
        if (content.includes('__UNIVERSAL_DATA_FOR_REHYDRATION__') || 
            content.includes('window.__INITIAL_STATE__')) {
          
          const dataMatch = content.match(/window\.__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?});/) ||
                           content.match(/window\.__INITIAL_STATE__\s*=\s*({.+?});/);
          
          if (dataMatch) {
            try {
              this.videoData = JSON.parse(dataMatch[1]);
              this.emit('videoDataExtracted', this.videoData);
            } catch (parseError) {
              console.error('Error parsing TikTok video data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error extracting TikTok video data:', error);
    }
  }

  /**
   * 设置自动播放检测
   */
  setupAutoplayDetection() {
    if (this.videoElement) {
      this.videoElement.addEventListener('play', () => {
        this.emit('autoplayDetected');
      });
    }
  }

  /**
   * 检查是否有字幕
   */
  async hasSubtitles() {
    // TikTok通常没有传统的字幕，但可能有自动生成的字幕
    const hasHtmlSubtitles = await super.hasSubtitles();
    if (hasHtmlSubtitles) {
      return true;
    }

    // 检查是否有自动字幕或文字覆盖
    const captionElements = document.querySelectorAll([
      '[data-e2e="video-caption"]',
      '.video-caption',
      '.auto-caption'
    ].join(','));

    return captionElements.length > 0;
  }

  /**
   * 提取平台特定字幕
   */
  async extractPlatformSubtitles() {
    const subtitles = [];

    try {
      // TikTok可能有自动生成的字幕数据
      if (this.videoData) {
        const captionData = this.extractCaptionFromVideoData();
        if (captionData.length > 0) {
          return captionData;
        }
      }

      // 检查页面中的字幕元素
      const captionElements = document.querySelectorAll([
        '[data-e2e="video-caption"]',
        '.video-caption'
      ].join(','));

      captionElements.forEach((element, index) => {
        const text = element.textContent.trim();
        if (text) {
          subtitles.push({
            id: `tiktok-caption-${index}`,
            text: text,
            startTime: 0,
            endTime: this.videoElement ? this.videoElement.duration : 30
          });
        }
      });

    } catch (error) {
      console.error('Error extracting TikTok subtitles:', error);
    }

    return subtitles;
  }

  /**
   * 从视频数据中提取字幕
   */
  extractCaptionFromVideoData() {
    const subtitles = [];

    try {
      if (this.videoData && this.videoData.default) {
        // 遍历视频数据查找字幕信息
        const findCaptions = (obj) => {
          if (typeof obj !== 'object' || obj === null) return;
          
          for (const key in obj) {
            if (key.includes('caption') || key.includes('subtitle')) {
              if (Array.isArray(obj[key])) {
                obj[key].forEach((caption, index) => {
                  if (caption.text) {
                    subtitles.push({
                      id: `tiktok-data-${index}`,
                      text: caption.text,
                      startTime: caption.start || 0,
                      endTime: caption.end || (caption.start || 0) + 3
                    });
                  }
                });
              }
            } else if (typeof obj[key] === 'object') {
              findCaptions(obj[key]);
            }
          }
        };

        findCaptions(this.videoData);
      }
    } catch (error) {
      console.error('Error extracting captions from video data:', error);
    }

    return subtitles;
  }

  /**
   * 获取视频信息
   */
  getVideoInfo() {
    const baseInfo = super.getVideoInfo();
    
    // 添加TikTok特定信息
    const tiktokInfo = {
      author: this.getAuthor(),
      likes: this.getLikeCount(),
      shares: this.getShareCount(),
      comments: this.getCommentCount(),
      music: this.getMusicInfo()
    };

    return { ...baseInfo, ...tiktokInfo };
  }

  /**
   * 获取作者信息
   */
  getAuthor() {
    const authorSelectors = [
      '[data-e2e="video-author"]',
      '.author-name',
      '.username'
    ];

    for (const selector of authorSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取点赞数
   */
  getLikeCount() {
    const likeSelectors = [
      '[data-e2e="like-count"]',
      '.like-count',
      '[data-e2e="video-like-count"]'
    ];

    for (const selector of likeSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取分享数
   */
  getShareCount() {
    const shareSelectors = [
      '[data-e2e="share-count"]',
      '.share-count'
    ];

    for (const selector of shareSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取评论数
   */
  getCommentCount() {
    const commentSelectors = [
      '[data-e2e="comment-count"]',
      '.comment-count'
    ];

    for (const selector of commentSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取音乐信息
   */
  getMusicInfo() {
    const musicSelectors = [
      '[data-e2e="video-music"]',
      '.music-info',
      '.sound-info'
    ];

    for (const selector of musicSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }
}
