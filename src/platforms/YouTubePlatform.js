/**
 * YouTube平台实现
 */

import { BasePlatform } from './BasePlatform.js';

export class YouTubePlatform extends BasePlatform {
  constructor() {
    super();
    this.name = 'youtube';
    this.playerApi = null;
  }

  /**
   * 检查是否支持当前URL
   */
  async isSupported(url) {
    return url.includes('youtube.com/watch') || url.includes('youtu.be/');
  }

  /**
   * 获取视频ID
   */
  getVideoId() {
    const urlParams = new URLSearchParams(window.location.search);
    const videoId = urlParams.get('v');
    
    if (videoId) {
      return videoId;
    }

    // 处理youtu.be短链接
    const match = window.location.href.match(/youtu\.be\/([^?&]+)/);
    return match ? match[1] : null;
  }

  /**
   * 获取视频标题
   */
  getVideoTitle() {
    // 尝试从多个可能的位置获取标题
    const titleSelectors = [
      'h1.title.style-scope.ytd-video-primary-info-renderer',
      'h1.ytd-video-primary-info-renderer',
      '.ytd-video-primary-info-renderer h1',
      '#container h1'
    ];

    for (const selector of titleSelectors) {
      const titleElement = document.querySelector(selector);
      if (titleElement) {
        return titleElement.textContent.trim();
      }
    }

    return document.title.replace(' - YouTube', '');
  }

  /**
   * 设置平台特定功能
   */
  async setupPlatformSpecificFeatures() {
    // 监听YouTube的SPA导航
    this.setupSPANavigation();
    
    // 尝试获取YouTube Player API
    this.setupPlayerAPI();
    
    // 设置字幕检测
    this.setupSubtitleDetection();
  }

  /**
   * 设置SPA导航监听
   */
  setupSPANavigation() {
    let lastUrl = location.href;
    
    const observer = new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        if (url.includes('/watch')) {
          // 页面切换到视频页面，重新初始化
          setTimeout(() => {
            this.findVideoElement();
            this.emit('navigationChanged', { url });
          }, 1000);
        }
      }
    });

    observer.observe(document, { subtree: true, childList: true });
  }

  /**
   * 设置Player API
   */
  setupPlayerAPI() {
    // 尝试获取YouTube内部播放器API
    try {
      const player = document.querySelector('#movie_player');
      if (player && player.getVideoData) {
        this.playerApi = player;
      }
    } catch (error) {
      console.log('YouTube Player API not available:', error);
    }
  }

  /**
   * 设置字幕检测
   */
  setupSubtitleDetection() {
    // 监听字幕按钮状态变化
    const subtitleButton = document.querySelector('.ytp-subtitles-button');
    if (subtitleButton) {
      const observer = new MutationObserver(() => {
        this.emit('subtitleStateChanged');
      });
      
      observer.observe(subtitleButton, {
        attributes: true,
        attributeFilter: ['aria-pressed']
      });
    }
  }

  /**
   * 提取平台特定字幕
   */
  async extractPlatformSubtitles() {
    const subtitles = [];

    try {
      // 方法1: 从YouTube Player API获取
      if (this.playerApi) {
        const apiSubtitles = await this.extractFromPlayerAPI();
        if (apiSubtitles.length > 0) {
          return apiSubtitles;
        }
      }

      // 方法2: 从页面数据获取
      const pageSubtitles = await this.extractFromPageData();
      if (pageSubtitles.length > 0) {
        return pageSubtitles;
      }

      // 方法3: 从字幕API获取
      const apiSubtitles = await this.extractFromSubtitleAPI();
      if (apiSubtitles.length > 0) {
        return apiSubtitles;
      }

    } catch (error) {
      console.error('YouTube subtitle extraction error:', error);
    }

    return subtitles;
  }

  /**
   * 从Player API提取字幕
   */
  async extractFromPlayerAPI() {
    const subtitles = [];

    try {
      if (this.playerApi && this.playerApi.getOption) {
        const captionTracks = this.playerApi.getOption('captions', 'tracklist');
        if (captionTracks && captionTracks.length > 0) {
          // 获取第一个可用的字幕轨道
          const track = captionTracks[0];
          if (track.url) {
            const response = await fetch(track.url);
            const xmlText = await response.text();
            return this.parseYouTubeXML(xmlText);
          }
        }
      }
    } catch (error) {
      console.error('Error extracting from Player API:', error);
    }

    return subtitles;
  }

  /**
   * 从页面数据提取字幕
   */
  async extractFromPageData() {
    const subtitles = [];

    try {
      // 查找页面中的字幕数据
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        const content = script.textContent;
        if (content.includes('captionTracks')) {
          const match = content.match(/"captionTracks":\s*(\[.*?\])/);
          if (match) {
            const tracks = JSON.parse(match[1]);
            if (tracks.length > 0) {
              const track = tracks[0];
              if (track.baseUrl) {
                const response = await fetch(track.baseUrl);
                const xmlText = await response.text();
                return this.parseYouTubeXML(xmlText);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error extracting from page data:', error);
    }

    return subtitles;
  }

  /**
   * 从字幕API提取字幕
   */
  async extractFromSubtitleAPI() {
    const subtitles = [];
    const videoId = this.getVideoId();

    if (!videoId) {
      return subtitles;
    }

    try {
      // 使用YouTube Data API v3获取字幕
      // 注意：这需要API密钥，在实际实现中可能需要后端代理
      const apiUrl = `https://www.googleapis.com/youtube/v3/captions?part=snippet&videoId=${videoId}`;
      
      // 这里应该通过后端代理调用，避免暴露API密钥
      console.log('YouTube subtitle API extraction not implemented yet');
      
    } catch (error) {
      console.error('Error extracting from subtitle API:', error);
    }

    return subtitles;
  }

  /**
   * 解析YouTube XML字幕格式
   */
  parseYouTubeXML(xmlText) {
    const subtitles = [];

    try {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
      const textElements = xmlDoc.querySelectorAll('text');

      textElements.forEach((element, index) => {
        const start = parseFloat(element.getAttribute('start') || '0');
        const duration = parseFloat(element.getAttribute('dur') || '0');
        const text = element.textContent || '';

        if (text.trim()) {
          subtitles.push({
            id: `youtube-${index}`,
            text: this.cleanSubtitleText(text),
            startTime: start,
            endTime: start + duration
          });
        }
      });

    } catch (error) {
      console.error('Error parsing YouTube XML:', error);
    }

    return subtitles;
  }

  /**
   * 清理字幕文本
   */
  cleanSubtitleText(text) {
    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .trim();
  }

  /**
   * 检查是否有字幕
   */
  async hasSubtitles() {
    // 首先检查HTML5 textTracks
    const hasHtmlSubtitles = await super.hasSubtitles();
    if (hasHtmlSubtitles) {
      return true;
    }

    // 检查YouTube特定的字幕
    try {
      // 检查字幕按钮是否存在
      const subtitleButton = document.querySelector('.ytp-subtitles-button');
      if (subtitleButton && !subtitleButton.disabled) {
        return true;
      }

      // 检查页面数据中是否有字幕信息
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent.includes('captionTracks')) {
          return true;
        }
      }

    } catch (error) {
      console.error('Error checking YouTube subtitles:', error);
    }

    return false;
  }

  /**
   * 获取视频信息
   */
  getVideoInfo() {
    const baseInfo = super.getVideoInfo();
    
    // 添加YouTube特定信息
    const youtubeInfo = {
      channel: this.getChannelName(),
      views: this.getViewCount(),
      likes: this.getLikeCount()
    };

    return { ...baseInfo, ...youtubeInfo };
  }

  /**
   * 获取频道名称
   */
  getChannelName() {
    const channelSelectors = [
      '#channel-name a',
      '.ytd-channel-name a',
      '#owner-name a'
    ];

    for (const selector of channelSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取观看次数
   */
  getViewCount() {
    const viewSelectors = [
      '.view-count',
      '#count .ytd-video-view-count-renderer'
    ];

    for (const selector of viewSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    return '';
  }

  /**
   * 获取点赞数
   */
  getLikeCount() {
    const likeSelectors = [
      '#top-level-buttons-computed button[aria-label*="like"] #text',
      '.ytd-toggle-button-renderer #text'
    ];

    for (const selector of likeSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.includes('like')) {
        return element.textContent.trim();
      }
    }

    return '';
  }
}
