/**
 * 批处理队列实现
 * 支持批量处理任务以提高效率
 */

import { IBatchQueue, TaskStatus } from '../core/interfaces/IQueue.js';
import { TaskQueue } from './TaskQueue.js';

export class BatchQueue extends IBatchQueue {
  constructor(processor, options = {}) {
    super();
    
    this.processor = processor;
    this.queue = [];
    this.processing = false;
    this.taskIdCounter = 0;
    
    // 批处理配置
    this.options = {
      batchSize: options.batchSize || processor.getOptimalBatchSize?.() || 10,
      maxWaitTime: options.maxWaitTime || 5000, // 最大等待时间（毫秒）
      minBatchSize: options.minBatchSize || 1,
      maxSize: options.maxSize || 1000,
      autoProcess: options.autoProcess !== false,
      ...options
    };

    // 统计信息
    this.stats = {
      totalEnqueued: 0,
      totalBatches: 0,
      totalProcessed: 0,
      totalFailed: 0,
      averageBatchSize: 0
    };

    // 批处理定时器
    this.batchTimer = null;
    this.pendingBatch = [];
  }

  /**
   * 生成唯一任务ID
   * @returns {string}
   */
  generateTaskId() {
    return `batch_task_${Date.now()}_${++this.taskIdCounter}`;
  }

  /**
   * 将任务加入队列
   * @param {Object} task 任务对象
   * @returns {Promise<string>} 任务ID
   */
  async enqueue(task) {
    if (this.queue.length >= this.options.maxSize) {
      throw new Error('Queue is full');
    }

    const taskId = this.generateTaskId();
    const queueTask = {
      id: taskId,
      data: task,
      status: TaskStatus.PENDING,
      createdAt: new Date(),
      batchId: null
    };

    this.queue.push(queueTask);
    this.stats.totalEnqueued++;

    this.emit('taskEnqueued', queueTask);

    // 如果启用自动处理，尝试形成批次
    if (this.options.autoProcess) {
      this.tryFormBatch();
    }

    return taskId;
  }

  /**
   * 批量加入任务
   * @param {Array<Object>} tasks 任务数组
   * @returns {Promise<Array<string>>} 任务ID数组
   */
  async enqueueBatch(tasks) {
    const taskIds = [];
    
    for (const task of tasks) {
      const taskId = await this.enqueue(task);
      taskIds.push(taskId);
    }

    return taskIds;
  }

  /**
   * 从队列中取出任务
   * @returns {Promise<Object|null>}
   */
  async dequeue() {
    if (this.queue.length === 0) {
      return null;
    }

    const task = this.queue.shift();
    this.emit('taskDequeued', task);
    return task;
  }

  /**
   * 批量取出任务
   * @param {number} count 取出数量
   * @returns {Promise<Array<Object>>}
   */
  async dequeueBatch(count) {
    const batch = [];
    const actualCount = Math.min(count, this.queue.length);

    for (let i = 0; i < actualCount; i++) {
      const task = await this.dequeue();
      if (task) {
        batch.push(task);
      }
    }

    return batch;
  }

  /**
   * 查看队列头部任务但不移除
   * @returns {Promise<Object|null>}
   */
  async peek() {
    return this.queue.length > 0 ? this.queue[0] : null;
  }

  /**
   * 获取队列大小
   * @returns {number}
   */
  size() {
    return this.queue.length;
  }

  /**
   * 清空队列
   * @returns {Promise<void>}
   */
  async clear() {
    const clearedTasks = [...this.queue];
    this.queue = [];
    this.pendingBatch = [];
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    this.emit('queueCleared', { clearedTasks });
  }

  /**
   * 获取队列中的所有任务
   * @returns {Array<Object>}
   */
  getAllTasks() {
    return [...this.queue];
  }

  /**
   * 根据ID查找任务
   * @param {string} taskId 任务ID
   * @returns {Object|null}
   */
  findTask(taskId) {
    return this.queue.find(task => task.id === taskId) || null;
  }

  /**
   * 移除指定任务
   * @param {string} taskId 任务ID
   * @returns {Promise<boolean>}
   */
  async removeTask(taskId) {
    const index = this.queue.findIndex(task => task.id === taskId);
    if (index !== -1) {
      const removedTask = this.queue.splice(index, 1)[0];
      this.emit('taskRemoved', removedTask);
      return true;
    }
    return false;
  }

  /**
   * 获取最佳批处理大小
   * @returns {number}
   */
  getOptimalBatchSize() {
    return this.options.batchSize;
  }

  /**
   * 设置批处理大小
   * @param {number} size 批处理大小
   */
  setBatchSize(size) {
    this.options.batchSize = Math.max(1, size);
  }

  /**
   * 尝试形成批次
   */
  tryFormBatch() {
    if (this.processing) {
      return;
    }

    // 检查是否有足够的任务形成批次
    if (this.queue.length >= this.options.batchSize) {
      this.processBatch();
    } else if (this.queue.length >= this.options.minBatchSize && !this.batchTimer) {
      // 设置定时器，等待更多任务或超时处理
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.options.maxWaitTime);
    }
  }

  /**
   * 处理批次
   */
  async processBatch() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    // 清除定时器
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    try {
      // 取出一批任务
      const batchSize = Math.min(this.options.batchSize, this.queue.length);
      const batch = await this.dequeueBatch(batchSize);
      
      if (batch.length === 0) {
        return;
      }

      const batchId = `batch_${Date.now()}_${this.stats.totalBatches}`;
      
      // 更新任务状态
      batch.forEach(task => {
        task.status = TaskStatus.PROCESSING;
        task.batchId = batchId;
        task.startedAt = new Date();
      });

      this.stats.totalBatches++;
      this.emit('batchStarted', { batchId, tasks: batch });

      try {
        // 使用处理器批量处理任务
        const inputs = batch.map(task => task.data);
        const results = await this.processor.batchProcess(inputs, {
          batchId,
          batchSize: batch.length
        });

        // 处理结果
        batch.forEach((task, index) => {
          task.status = TaskStatus.COMPLETED;
          task.completedAt = new Date();
          task.result = results[index];
          this.stats.totalProcessed++;
        });

        // 更新平均批次大小
        this.updateAverageBatchSize(batch.length);

        this.emit('batchCompleted', { batchId, tasks: batch, results });

      } catch (error) {
        // 批处理失败，标记所有任务为失败
        batch.forEach(task => {
          task.status = TaskStatus.FAILED;
          task.failedAt = new Date();
          task.error = error;
          this.stats.totalFailed++;
        });

        this.emit('batchFailed', { batchId, tasks: batch, error });
      }

    } finally {
      this.processing = false;
      
      // 继续处理剩余任务
      setTimeout(() => this.tryFormBatch(), 0);
    }
  }

  /**
   * 更新平均批次大小
   * @param {number} batchSize 当前批次大小
   */
  updateAverageBatchSize(batchSize) {
    const totalBatches = this.stats.totalBatches;
    this.stats.averageBatchSize = 
      (this.stats.averageBatchSize * (totalBatches - 1) + batchSize) / totalBatches;
  }

  /**
   * 获取队列统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      ...super.getStatistics(),
      ...this.stats,
      processorName: this.processor.getName(),
      processorAvailable: this.processor.isAvailable(),
      batchSize: this.options.batchSize,
      pendingBatchSize: this.pendingBatch.length
    };
  }

  /**
   * 启动队列处理
   */
  start() {
    this.options.autoProcess = true;
    this.tryFormBatch();
    this.emit('queueStarted');
  }

  /**
   * 停止队列处理
   */
  stop() {
    this.options.autoProcess = false;
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
    this.emit('queueStopped');
  }

  /**
   * 销毁队列
   */
  async destroy() {
    await this.clear();
    this.removeAllListeners();
  }
}
