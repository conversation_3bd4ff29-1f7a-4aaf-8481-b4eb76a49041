/**
 * 通用任务队列实现
 * 支持FIFO队列操作和事件通知
 */

import { IQueue, TaskStatus } from '../core/interfaces/IQueue.js';
import { EventEmitter } from '../core/EventEmitter.js';

export class TaskQueue extends IQueue {
  constructor(processor, options = {}) {
    super();
    
    this.processor = processor;
    this.queue = [];
    this.processing = false;
    this.taskIdCounter = 0;
    
    // 配置选项
    this.options = {
      maxSize: options.maxSize || 1000,
      autoProcess: options.autoProcess !== false,
      concurrency: options.concurrency || 1,
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 1000,
      ...options
    };

    // 统计信息
    this.stats = {
      totalEnqueued: 0,
      totalProcessed: 0,
      totalFailed: 0,
      currentlyProcessing: 0
    };

    // 当前处理的任务
    this.activeTasks = new Map();
  }

  /**
   * 生成唯一任务ID
   * @returns {string}
   */
  generateTaskId() {
    return `task_${Date.now()}_${++this.taskIdCounter}`;
  }

  /**
   * 将任务加入队列
   * @param {Object} task 任务对象
   * @returns {Promise<string>} 任务ID
   */
  async enqueue(task) {
    if (this.queue.length >= this.options.maxSize) {
      throw new Error('Queue is full');
    }

    const taskId = this.generateTaskId();
    const queueTask = {
      id: taskId,
      data: task,
      status: TaskStatus.PENDING,
      createdAt: new Date(),
      attempts: 0,
      maxAttempts: this.options.retryAttempts,
      lastError: null
    };

    this.queue.push(queueTask);
    this.stats.totalEnqueued++;

    this.emit('taskEnqueued', queueTask);

    // 如果启用自动处理，立即开始处理
    if (this.options.autoProcess) {
      this.processQueue();
    }

    return taskId;
  }

  /**
   * 从队列中取出任务
   * @returns {Promise<Object|null>}
   */
  async dequeue() {
    if (this.queue.length === 0) {
      return null;
    }

    const task = this.queue.shift();
    this.emit('taskDequeued', task);
    return task;
  }

  /**
   * 查看队列头部任务但不移除
   * @returns {Promise<Object|null>}
   */
  async peek() {
    return this.queue.length > 0 ? this.queue[0] : null;
  }

  /**
   * 获取队列大小
   * @returns {number}
   */
  size() {
    return this.queue.length;
  }

  /**
   * 清空队列
   * @returns {Promise<void>}
   */
  async clear() {
    const clearedTasks = [...this.queue];
    this.queue = [];
    
    // 取消所有活跃任务
    for (const [taskId, task] of this.activeTasks) {
      task.status = TaskStatus.CANCELLED;
      this.emit('taskCancelled', task);
    }
    this.activeTasks.clear();

    this.emit('queueCleared', { clearedTasks });
  }

  /**
   * 获取队列中的所有任务
   * @returns {Array<Object>}
   */
  getAllTasks() {
    return [...this.queue];
  }

  /**
   * 根据ID查找任务
   * @param {string} taskId 任务ID
   * @returns {Object|null}
   */
  findTask(taskId) {
    return this.queue.find(task => task.id === taskId) || 
           this.activeTasks.get(taskId) || 
           null;
  }

  /**
   * 移除指定任务
   * @param {string} taskId 任务ID
   * @returns {Promise<boolean>}
   */
  async removeTask(taskId) {
    const index = this.queue.findIndex(task => task.id === taskId);
    if (index !== -1) {
      const removedTask = this.queue.splice(index, 1)[0];
      this.emit('taskRemoved', removedTask);
      return true;
    }

    // 如果任务正在处理中，标记为取消
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId);
      task.status = TaskStatus.CANCELLED;
      this.emit('taskCancelled', task);
      return true;
    }

    return false;
  }

  /**
   * 处理队列中的任务
   */
  async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    if (this.activeTasks.size >= this.options.concurrency) {
      return;
    }

    this.processing = true;

    try {
      while (this.queue.length > 0 && this.activeTasks.size < this.options.concurrency) {
        const task = await this.dequeue();
        if (task) {
          this.processTask(task);
        }
      }
    } finally {
      this.processing = false;
    }
  }

  /**
   * 处理单个任务
   * @param {Object} task 任务对象
   */
  async processTask(task) {
    task.status = TaskStatus.PROCESSING;
    task.startedAt = new Date();
    this.activeTasks.set(task.id, task);
    this.stats.currentlyProcessing++;

    this.emit('taskStarted', task);

    try {
      // 使用处理器处理任务
      const result = await this.processor.process(task.data, {
        taskId: task.id,
        attempt: task.attempts + 1
      });

      task.status = TaskStatus.COMPLETED;
      task.completedAt = new Date();
      task.result = result;
      this.stats.totalProcessed++;

      this.emit('taskCompleted', task);

    } catch (error) {
      task.attempts++;
      task.lastError = error;

      if (task.attempts < task.maxAttempts) {
        // 重试任务
        task.status = TaskStatus.PENDING;
        setTimeout(() => {
          this.queue.unshift(task); // 重新加入队列头部
          this.processQueue();
        }, this.options.retryDelay);

        this.emit('taskRetry', task);
      } else {
        // 任务失败
        task.status = TaskStatus.FAILED;
        task.failedAt = new Date();
        this.stats.totalFailed++;

        this.emit('taskFailed', task);
      }
    } finally {
      this.activeTasks.delete(task.id);
      this.stats.currentlyProcessing--;

      // 继续处理队列中的其他任务
      setTimeout(() => this.processQueue(), 0);
    }
  }

  /**
   * 获取队列统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      ...super.getStatistics(),
      ...this.stats,
      activeTasks: this.activeTasks.size,
      processorName: this.processor.getName(),
      processorAvailable: this.processor.isAvailable(),
      options: { ...this.options }
    };
  }

  /**
   * 启动队列处理
   */
  start() {
    this.options.autoProcess = true;
    this.processQueue();
    this.emit('queueStarted');
  }

  /**
   * 停止队列处理
   */
  stop() {
    this.options.autoProcess = false;
    this.emit('queueStopped');
  }

  /**
   * 销毁队列
   */
  async destroy() {
    await this.clear();
    this.removeAllListeners();
  }
}
