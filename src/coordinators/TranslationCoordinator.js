/**
 * 翻译协调器
 * 管理整个翻译流程，协调翻译队列和TTS队列
 */

import { ITranslationCoordinator, CoordinatorStatus, TaskType } from '../core/interfaces/ICoordinator.js';
import { TaskQueue } from '../queue/TaskQueue.js';
import { BatchQueue } from '../queue/BatchQueue.js';
import { GoogleTranslateProcessor } from '../processors/translation/GoogleTranslateProcessor.js';
import { GoogleTTSProcessor } from '../processors/tts/GoogleTTSProcessor.js';

export class TranslationCoordinator extends ITranslationCoordinator {
  constructor(config = {}) {
    super();
    
    this.config = {
      translationBatchSize: 50,
      ttsBatchSize: 20,
      maxConcurrentTasks: 5,
      enableTTS: true,
      ...config
    };

    this.status = CoordinatorStatus.IDLE;
    this.activeTasks = new Map();
    this.taskResults = new Map();
    this.taskIdCounter = 0;

    // 初始化处理器
    this.translationProcessor = new GoogleTranslateProcessor(config.translation || {});
    this.ttsProcessor = new GoogleTTSProcessor(config.tts || {});

    // 初始化队列
    this.translationQueue = new BatchQueue(this.translationProcessor, {
      batchSize: this.config.translationBatchSize,
      autoProcess: true
    });

    this.ttsQueue = new BatchQueue(this.ttsProcessor, {
      batchSize: this.config.ttsBatchSize,
      autoProcess: true
    });

    // 绑定队列事件
    this.setupQueueEvents();
  }

  /**
   * 设置队列事件监听
   */
  setupQueueEvents() {
    // 翻译队列事件
    this.translationQueue.on('batchCompleted', (event) => {
      this.handleTranslationBatchCompleted(event);
    });

    this.translationQueue.on('batchFailed', (event) => {
      this.handleTranslationBatchFailed(event);
    });

    // TTS队列事件
    this.ttsQueue.on('batchCompleted', (event) => {
      this.handleTTSBatchCompleted(event);
    });

    this.ttsQueue.on('batchFailed', (event) => {
      this.handleTTSBatchFailed(event);
    });
  }

  /**
   * 初始化协调器
   * @param {Object} config 配置对象
   * @returns {Promise<void>}
   */
  async initialize(config = {}) {
    this.status = CoordinatorStatus.INITIALIZING;
    
    try {
      // 更新配置
      this.config = { ...this.config, ...config };
      
      // 更新处理器配置
      if (config.translation) {
        this.translationProcessor.updateConfig(config.translation);
      }
      
      if (config.tts) {
        this.ttsProcessor.updateConfig(config.tts);
      }

      // 检查处理器可用性
      if (!this.translationProcessor.isAvailable()) {
        throw new Error('Translation processor is not available');
      }

      if (this.config.enableTTS && !this.ttsProcessor.isAvailable()) {
        console.warn('TTS processor is not available, TTS will be disabled');
        this.config.enableTTS = false;
      }

      this.status = CoordinatorStatus.IDLE;
      this.emit('initialized');

    } catch (error) {
      this.status = CoordinatorStatus.ERROR;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 启动协调器
   * @param {Object} options 启动选项
   * @returns {Promise<void>}
   */
  async start(options = {}) {
    if (this.status !== CoordinatorStatus.IDLE) {
      throw new Error(`Cannot start coordinator in ${this.status} status`);
    }

    this.status = CoordinatorStatus.RUNNING;
    
    // 启动队列
    this.translationQueue.start();
    if (this.config.enableTTS) {
      this.ttsQueue.start();
    }

    this.emit('started');
  }

  /**
   * 停止协调器
   * @returns {Promise<void>}
   */
  async stop() {
    this.status = CoordinatorStatus.STOPPING;

    try {
      // 停止队列
      this.translationQueue.stop();
      this.ttsQueue.stop();

      // 等待当前任务完成
      await this.waitForActiveTasks();

      this.status = CoordinatorStatus.STOPPED;
      this.emit('stopped');

    } catch (error) {
      this.status = CoordinatorStatus.ERROR;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 暂停协调器
   * @returns {Promise<void>}
   */
  async pause() {
    if (this.status !== CoordinatorStatus.RUNNING) {
      throw new Error(`Cannot pause coordinator in ${this.status} status`);
    }

    this.status = CoordinatorStatus.PAUSED;
    
    // 暂停队列
    this.translationQueue.stop();
    this.ttsQueue.stop();

    this.emit('paused');
  }

  /**
   * 恢复协调器
   * @returns {Promise<void>}
   */
  async resume() {
    if (this.status !== CoordinatorStatus.PAUSED) {
      throw new Error(`Cannot resume coordinator in ${this.status} status`);
    }

    this.status = CoordinatorStatus.RUNNING;
    
    // 恢复队列
    this.translationQueue.start();
    if (this.config.enableTTS) {
      this.ttsQueue.start();
    }

    this.emit('resumed');
  }

  /**
   * 获取协调器状态
   * @returns {string}
   */
  getStatus() {
    return this.status;
  }

  /**
   * 更新配置
   * @param {Object} config 新配置
   * @returns {Promise<void>}
   */
  async updateConfig(config) {
    this.config = { ...this.config, ...config };
    
    // 更新处理器配置
    if (config.translation) {
      this.translationProcessor.updateConfig(config.translation);
    }
    
    if (config.tts) {
      this.ttsProcessor.updateConfig(config.tts);
    }

    this.emit('configUpdated', config);
  }

  /**
   * 开始翻译任务
   * @param {Array} subtitles 字幕数组
   * @param {Object} options 翻译选项
   * @returns {Promise<string>} 任务ID
   */
  async startTranslation(subtitles, options = {}) {
    if (this.status !== CoordinatorStatus.RUNNING) {
      throw new Error(`Cannot start translation in ${this.status} status`);
    }

    const taskId = this.generateTaskId();
    const task = {
      id: taskId,
      type: TaskType.TRANSLATION,
      subtitles: subtitles,
      options: {
        targetLanguage: 'zh-CN',
        enableTTS: this.config.enableTTS,
        ...options
      },
      status: 'processing',
      createdAt: new Date(),
      progress: {
        total: subtitles.length,
        translated: 0,
        synthesized: 0
      },
      results: []
    };

    this.activeTasks.set(taskId, task);

    try {
      // 创建翻译任务
      const translationTasks = subtitles.map((subtitle, index) => ({
        text: subtitle.text,
        index: index,
        taskId: taskId,
        startTime: subtitle.startTime,
        endTime: subtitle.endTime,
        originalText: subtitle.text
      }));

      // 将翻译任务加入队列
      for (const translationTask of translationTasks) {
        await this.translationQueue.enqueue(translationTask);
      }

      this.emit('translationStarted', { taskId, task });
      return taskId;

    } catch (error) {
      this.activeTasks.delete(taskId);
      throw error;
    }
  }

  /**
   * 停止翻译任务
   * @param {string} taskId 任务ID
   * @returns {Promise<void>}
   */
  async stopTranslation(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    task.status = 'cancelled';
    this.activeTasks.delete(taskId);

    this.emit('translationStopped', { taskId });
  }

  /**
   * 获取翻译进度
   * @param {string} taskId 任务ID
   * @returns {Object}
   */
  getTranslationProgress(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) {
      return null;
    }

    return {
      taskId,
      status: task.status,
      progress: task.progress,
      createdAt: task.createdAt
    };
  }

  /**
   * 获取翻译结果
   * @param {string} taskId 任务ID
   * @returns {Promise<Array>}
   */
  async getTranslationResult(taskId) {
    const result = this.taskResults.get(taskId);
    return result ? result.results : null;
  }

  /**
   * 处理翻译批次完成
   * @param {Object} event 事件对象
   */
  handleTranslationBatchCompleted(event) {
    const { tasks, results } = event;
    
    tasks.forEach((task, index) => {
      const activeTask = this.activeTasks.get(task.data.taskId);
      if (!activeTask) return;

      const translatedText = results[index];
      const resultItem = {
        index: task.data.index,
        startTime: task.data.startTime,
        endTime: task.data.endTime,
        originalText: task.data.originalText,
        translatedText: translatedText,
        audioUrl: null // 将在TTS完成后填充
      };

      activeTask.results[task.data.index] = resultItem;
      activeTask.progress.translated++;

      // 如果启用TTS，将翻译结果加入TTS队列
      if (activeTask.options.enableTTS && translatedText) {
        this.ttsQueue.enqueue({
          text: translatedText,
          taskId: task.data.taskId,
          index: task.data.index,
          languageCode: this.getLanguageCodeFromTarget(activeTask.options.targetLanguage)
        });
      }

      this.emit('translationProgress', {
        taskId: task.data.taskId,
        progress: activeTask.progress
      });
    });
  }

  /**
   * 处理翻译批次失败
   * @param {Object} event 事件对象
   */
  handleTranslationBatchFailed(event) {
    const { tasks, error } = event;
    
    tasks.forEach(task => {
      const activeTask = this.activeTasks.get(task.data.taskId);
      if (!activeTask) return;

      activeTask.status = 'failed';
      activeTask.error = error;

      this.emit('translationFailed', {
        taskId: task.data.taskId,
        error
      });
    });
  }

  /**
   * 处理TTS批次完成
   * @param {Object} event 事件对象
   */
  handleTTSBatchCompleted(event) {
    const { tasks, results } = event;
    
    tasks.forEach((task, index) => {
      const activeTask = this.activeTasks.get(task.data.taskId);
      if (!activeTask) return;

      const audioUrl = results[index];
      if (activeTask.results[task.data.index]) {
        activeTask.results[task.data.index].audioUrl = audioUrl;
      }

      activeTask.progress.synthesized++;

      // 检查任务是否完成
      if (activeTask.progress.synthesized >= activeTask.progress.total) {
        this.completeTask(task.data.taskId);
      }

      this.emit('ttsProgress', {
        taskId: task.data.taskId,
        progress: activeTask.progress
      });
    });
  }

  /**
   * 处理TTS批次失败
   * @param {Object} event 事件对象
   */
  handleTTSBatchFailed(event) {
    const { tasks, error } = event;
    
    tasks.forEach(task => {
      const activeTask = this.activeTasks.get(task.data.taskId);
      if (!activeTask) return;

      // TTS失败不影响翻译结果，只是没有音频
      activeTask.progress.synthesized++;

      this.emit('ttsFailed', {
        taskId: task.data.taskId,
        index: task.data.index,
        error
      });
    });
  }

  /**
   * 完成任务
   * @param {string} taskId 任务ID
   */
  completeTask(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) return;

    task.status = 'completed';
    task.completedAt = new Date();

    // 将结果移到结果存储
    this.taskResults.set(taskId, {
      results: task.results.filter(r => r !== undefined).sort((a, b) => a.index - b.index),
      completedAt: task.completedAt
    });

    this.activeTasks.delete(taskId);

    this.emit('translationCompleted', {
      taskId,
      results: this.taskResults.get(taskId).results
    });
  }

  /**
   * 等待活跃任务完成
   * @returns {Promise<void>}
   */
  async waitForActiveTasks() {
    while (this.activeTasks.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * 生成任务ID
   * @returns {string}
   */
  generateTaskId() {
    return `translation_${Date.now()}_${++this.taskIdCounter}`;
  }

  /**
   * 从目标语言获取语言代码
   * @param {string} targetLanguage 目标语言
   * @returns {string}
   */
  getLanguageCodeFromTarget(targetLanguage) {
    const languageMap = {
      'zh-CN': 'zh-CN',
      'zh-TW': 'zh-TW',
      'en': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'es': 'es-ES',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'ru': 'ru-RU'
    };

    return languageMap[targetLanguage] || 'zh-CN';
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStatistics() {
    return {
      status: this.status,
      activeTasks: this.activeTasks.size,
      completedTasks: this.taskResults.size,
      translationQueue: this.translationQueue.getStatistics(),
      ttsQueue: this.ttsQueue.getStatistics(),
      translationProcessor: this.translationProcessor.getStatistics(),
      ttsProcessor: this.ttsProcessor.getStatistics()
    };
  }

  /**
   * 销毁协调器
   * @returns {Promise<void>}
   */
  async destroy() {
    await this.stop();
    
    // 清理队列
    await this.translationQueue.destroy();
    await this.ttsQueue.destroy();
    
    // 清理数据
    this.activeTasks.clear();
    this.taskResults.clear();
    
    this.removeAllListeners();
  }
}
