/**
 * ChronoTranslate V3.0 使用示例
 * 展示新架构的简洁API使用方法
 */

import { ChronoTranslateV3 } from '../src/core/ChronoTranslateV3.js';

// ========== 基础使用示例 ==========

async function basicUsageExample() {
  console.log('=== 基础使用示例 ===');
  
  // 创建实例（自动初始化）
  const translator = new ChronoTranslateV3({
    debug: true,
    autoInit: true
  });

  // 等待初始化完成
  await translator.waitForReady();

  // 设置API密钥
  await translator.setApiKeys({
    translation: 'your-google-translate-api-key',
    tts: 'your-google-tts-api-key'
  });

  // 设置目标语言
  await translator.setTargetLanguage('zh-CN');

  // 快速翻译当前页面的视频
  try {
    const taskId = await translator.quickTranslate({
      enableTTS: true,
      voice: 'zh-CN-Wavenet-A'
    });

    console.log(`翻译任务已启动，任务ID: ${taskId}`);

    // 监听进度
    translator.on('translationProgress', (event) => {
      const { progress } = event;
      console.log(`翻译进度: ${progress.translated}/${progress.total}`);
    });

    // 监听完成
    translator.on('translationCompleted', async (event) => {
      console.log('翻译完成！');
      const results = await translator.getResult(event.taskId);
      console.log('翻译结果:', results);
    });

  } catch (error) {
    console.error('翻译失败:', error);
  }
}

// ========== 高级使用示例 ==========

async function advancedUsageExample() {
  console.log('=== 高级使用示例 ===');

  // 手动初始化
  const translator = new ChronoTranslateV3({
    debug: true,
    autoInit: false
  });

  // 自定义配置初始化
  await translator.init({
    translationApiKey: 'your-api-key',
    ttsApiKey: 'your-tts-api-key',
    targetLanguage: 'ja',
    enableTTS: true,
    batchSize: 20,
    maxConcurrency: 5
  });

  // 手动提供字幕数据
  const subtitles = [
    { startTime: 0, endTime: 2, text: "Hello, welcome to our video!" },
    { startTime: 2, endTime: 5, text: "Today we'll learn about AI translation." },
    { startTime: 5, endTime: 8, text: "This technology is amazing!" }
  ];

  // 开始翻译
  const taskId = await translator.translateVideo(subtitles, {
    targetLanguage: 'zh-CN',
    enableTTS: true,
    voice: 'zh-CN-Wavenet-B'
  });

  // 实时监控进度
  const progressInterval = setInterval(() => {
    const progress = translator.getProgress(taskId);
    if (progress) {
      console.log(`进度: ${progress.progress.translated}/${progress.progress.total} (TTS: ${progress.progress.synthesized})`);
      
      if (progress.status === 'completed') {
        clearInterval(progressInterval);
        console.log('任务完成！');
      }
    }
  }, 1000);

  // 获取统计信息
  setTimeout(() => {
    const stats = translator.getStatistics();
    console.log('系统统计:', stats);
  }, 5000);
}

// ========== 事件处理示例 ==========

async function eventHandlingExample() {
  console.log('=== 事件处理示例 ===');

  const translator = new ChronoTranslateV3();
  await translator.waitForReady();

  // 监听所有事件
  translator.on('translationStarted', (event) => {
    console.log('🚀 翻译开始:', event.taskId);
  });

  translator.on('translationProgress', (event) => {
    const { taskId, progress } = event;
    const percentage = Math.round((progress.translated / progress.total) * 100);
    console.log(`📊 翻译进度 [${taskId}]: ${percentage}%`);
  });

  translator.on('ttsProgress', (event) => {
    const { taskId, progress } = event;
    const percentage = Math.round((progress.synthesized / progress.total) * 100);
    console.log(`🔊 TTS进度 [${taskId}]: ${percentage}%`);
  });

  translator.on('translationCompleted', async (event) => {
    console.log('✅ 翻译完成:', event.taskId);
    
    // 获取结果并播放
    const results = await translator.getResult(event.taskId);
    playTranslatedAudio(results);
  });

  translator.on('translationFailed', (event) => {
    console.error('❌ 翻译失败:', event.error);
  });

  translator.on('error', (error) => {
    console.error('💥 系统错误:', error);
  });

  // 开始翻译
  await translator.quickTranslate();
}

// ========== 配置管理示例 ==========

async function configManagementExample() {
  console.log('=== 配置管理示例 ===');

  const translator = new ChronoTranslateV3();
  await translator.waitForReady();

  // 获取当前配置
  const currentConfig = await translator.getConfig();
  console.log('当前配置:', currentConfig);

  // 更新配置
  await translator.updateConfig({
    targetLanguage: 'ja',
    enableTTS: true,
    speakingRate: 1.2,
    pitch: 2.0,
    batchSize: 15
  });

  // 批量设置API密钥
  await translator.setApiKeys({
    google: 'your-google-api-key'
  });

  // 设置语言和TTS
  await translator.setTargetLanguage('ko');
  await translator.setTTSEnabled(false);

  console.log('配置更新完成');
}

// ========== 错误处理示例 ==========

async function errorHandlingExample() {
  console.log('=== 错误处理示例 ===');

  const translator = new ChronoTranslateV3();

  try {
    // 在未初始化时尝试翻译
    await translator.translateVideo([]);
  } catch (error) {
    console.log('预期错误 - 未初始化:', error.message);
  }

  await translator.waitForReady();

  try {
    // 尝试翻译空字幕
    await translator.translateVideo([]);
  } catch (error) {
    console.log('预期错误 - 空字幕:', error.message);
  }

  try {
    // 尝试翻译不存在的视频
    await translator.translateVideo(null);
  } catch (error) {
    console.log('预期错误 - 无效输入:', error.message);
  }
}

// ========== 辅助函数 ==========

function playTranslatedAudio(results) {
  console.log('播放翻译音频...');
  
  // 按时间顺序播放音频
  results.forEach((result, index) => {
    if (result.audioUrl) {
      setTimeout(() => {
        const audio = new Audio(result.audioUrl);
        audio.play().catch(console.error);
        console.log(`播放: ${result.translatedText}`);
      }, result.startTime * 1000);
    }
  });
}

// ========== 运行示例 ==========

async function runExamples() {
  console.log('ChronoTranslate V3.0 使用示例');
  console.log('================================');

  try {
    // 运行基础示例
    await basicUsageExample();
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行高级示例
    await advancedUsageExample();
    
    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 运行事件处理示例
    await eventHandlingExample();
    
    // 运行配置管理示例
    await configManagementExample();
    
    // 运行错误处理示例
    await errorHandlingExample();

  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runChronoTranslateExamples = runExamples;
  console.log('示例已加载，调用 runChronoTranslateExamples() 来运行');
} else {
  // Node.js环境
  runExamples();
}

export {
  basicUsageExample,
  advancedUsageExample,
  eventHandlingExample,
  configManagementExample,
  errorHandlingExample,
  runExamples
};
