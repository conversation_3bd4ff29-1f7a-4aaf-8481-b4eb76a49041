<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>ChronoTranslate 设置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      background: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #f0f0f0;
    }
    
    .logo {
      font-size: 32px;
      font-weight: bold;
      color: #1976d2;
      margin-bottom: 10px;
    }
    
    .subtitle {
      color: #666;
      font-size: 16px;
    }
    
    .section {
      margin-bottom: 40px;
    }
    
    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .section-title::before {
      content: '';
      width: 4px;
      height: 20px;
      background-color: #1976d2;
      border-radius: 2px;
    }
    
    .form-group {
      margin-bottom: 25px;
    }
    
    .form-label {
      display: block;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
      font-size: 14px;
    }
    
    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.2s;
      box-sizing: border-box;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #1976d2;
      box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    }
    
    .form-input[type="password"] {
      font-family: monospace;
    }
    
    .form-help {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
      line-height: 1.4;
    }
    
    .form-help a {
      color: #1976d2;
      text-decoration: none;
    }
    
    .form-help a:hover {
      text-decoration: underline;
    }
    
    .form-select {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      background-color: white;
      cursor: pointer;
      box-sizing: border-box;
    }
    
    .form-select:focus {
      outline: none;
      border-color: #1976d2;
      box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      margin-right: 10px;
    }
    
    .btn-primary {
      background-color: #1976d2;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #1565c0;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
    }
    
    .btn-secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 2px solid #e0e0e0;
    }
    
    .btn-secondary:hover {
      background-color: #e0e0e0;
    }
    
    .status-message {
      padding: 12px 16px;
      border-radius: 8px;
      margin-top: 20px;
      font-size: 14px;
      display: none;
    }
    
    .status-success {
      background-color: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }
    
    .status-error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
    
    .api-status {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
    
    .api-status.configured {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    
    .api-status.not-configured {
      background-color: #fff3e0;
      color: #f57c00;
    }
    
    .warning-box {
      background-color: #fff3e0;
      border: 1px solid #ffcc02;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 20px;
    }
    
    .warning-box h4 {
      margin: 0 0 10px 0;
      color: #f57c00;
      font-size: 16px;
    }
    
    .warning-box p {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      color: #999;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🌐 ChronoTranslate</div>
      <div class="subtitle">YouTube实时翻译插件设置</div>
    </div>

    <div class="warning-box">
      <h4>⚠️ 隐私与安全提醒</h4>
      <p>您的API密钥将安全地存储在本地浏览器中，不会上传到任何服务器。请妥善保管您的API密钥，不要与他人分享。</p>
    </div>

    <div class="section">
      <div class="section-title">API 配置</div>
      
      <div class="form-group">
        <label class="form-label">
          Google Translate API Key
          <span class="api-status not-configured" id="google-translate-status">未配置</span>
        </label>
        <input type="password" class="form-input" id="googleTranslateKey" placeholder="输入您的Google Translate API密钥">
        <div class="form-help">
          用于文本翻译服务。<a href="https://cloud.google.com/translate/docs/setup" target="_blank">获取API密钥</a>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">
          Google Text-to-Speech API Key
          <span class="api-status not-configured" id="google-tts-status">未配置</span>
        </label>
        <input type="password" class="form-input" id="googleTTSKey" placeholder="输入您的Google TTS API密钥">
        <div class="form-help">
          用于语音合成服务。<a href="https://cloud.google.com/text-to-speech/docs/quickstart" target="_blank">获取API密钥</a>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">
          OpenAI API Key
          <span class="api-status not-configured" id="openai-status">未配置</span>
        </label>
        <input type="password" class="form-input" id="openaiKey" placeholder="输入您的OpenAI API密钥">
        <div class="form-help">
          用于语音识别、翻译和语音合成的备选服务。<a href="https://platform.openai.com/api-keys" target="_blank">获取API密钥</a>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">偏好设置</div>
      
      <div class="form-group">
        <label class="form-label">默认目标语言</label>
        <select class="form-select" id="defaultLanguage">
          <option value="zh-CN">中文 (简体)</option>
          <option value="zh-TW">中文 (繁體)</option>
          <option value="ja">日本語</option>
          <option value="ko">한국어</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
          <option value="ru">Русский</option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">语音音色偏好</label>
        <select class="form-select" id="voicePreference">
          <option value="neutral">中性</option>
          <option value="male">男声</option>
          <option value="female">女声</option>
        </select>
      </div>
    </div>

    <div class="section">
      <button class="btn btn-primary" id="saveBtn">保存设置</button>
      <button class="btn btn-secondary" id="testBtn">测试连接</button>
    </div>

    <div class="status-message" id="statusMessage"></div>

    <div class="footer">
      ChronoTranslate v1.0.0 | 开源项目
    </div>
  </div>

  <script src="options.js"></script>
</body>
</html>
