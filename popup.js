// popup.js - 弹出窗口逻辑
document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const openOptionsBtn = document.getElementById('openOptions');
  const toggleTranslationBtn = document.getElementById('toggleTranslation');

  // 检查当前标签页是否为YouTube视频页面
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];
    if (currentTab.url && currentTab.url.includes('youtube.com/watch')) {
      statusDiv.textContent = '准备就绪';
      statusDiv.className = 'status active';
      toggleTranslationBtn.disabled = false;
      
      // 检查翻译状态
      chrome.tabs.sendMessage(currentTab.id, {action: 'getStatus'}, function(response) {
        if (chrome.runtime.lastError) {
          console.log('Content script not ready yet');
          return;
        }
        
        if (response && response.isTranslating) {
          statusDiv.textContent = '翻译进行中';
          toggleTranslationBtn.textContent = '停止翻译';
        } else {
          statusDiv.textContent = '准备就绪';
          toggleTranslationBtn.textContent = '启动翻译';
        }
      });
    } else {
      statusDiv.textContent = '请在YouTube视频页面使用';
      statusDiv.className = 'status inactive';
      toggleTranslationBtn.disabled = true;
    }
  });

  // 打开设置页面
  openOptionsBtn.addEventListener('click', function() {
    chrome.runtime.openOptionsPage();
  });

  // 切换翻译状态
  toggleTranslationBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const currentTab = tabs[0];
      chrome.tabs.sendMessage(currentTab.id, {action: 'toggleTranslation'}, function(response) {
        if (chrome.runtime.lastError) {
          console.error('Error:', chrome.runtime.lastError);
          return;
        }
        
        if (response && response.isTranslating) {
          statusDiv.textContent = '翻译进行中';
          toggleTranslationBtn.textContent = '停止翻译';
        } else {
          statusDiv.textContent = '准备就绪';
          toggleTranslationBtn.textContent = '启动翻译';
        }
      });
    });
  });
});
