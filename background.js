// background.js - Service Worker 后台脚本
class ChronoTranslateBackground {
  constructor() {
    this.isCapturing = false;
    this.audioStream = null;
    this.audioContext = null;
    this.processor = null;
    this.currentTabId = null;
    this.apiKeys = {};
    this.audioBuffer = [];
    this.processingQueue = [];
    this.isProcessing = false;
    this.chunkCounter = 0;
    this.maxConcurrentProcessing = 3; // 最大并发处理数

    this.init();
  }

  init() {
    // 监听消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放以支持异步响应
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com/watch')) {
        this.currentTabId = tabId;
      }
    });

    // 媒体嗅探：监听网络请求以捕获视频URL
    this.setupMediaSniffing();

    // 加载API密钥
    this.loadApiKeys();
  }

  setupMediaSniffing() {
    // 监听所有网络请求，寻找媒体资源
    chrome.webRequest.onBeforeSendHeaders.addListener(
      (details) => {
        this.handleMediaRequest(details);
      },
      {
        urls: ["<all_urls>"],
        types: ["xmlhttprequest", "media", "other"]
      },
      ["requestHeaders"]
    );
  }

  handleMediaRequest(details) {
    const url = details.url;
    const tabId = details.tabId;

    // 检查是否为媒体资源
    if (this.isMediaUrl(url)) {
      console.log('ChronoTranslate Background: Media URL detected:', url);

      // 存储媒体信息到session storage
      chrome.storage.session.set({
        [`media_${tabId}`]: {
          videoUrl: url,
          tabId: tabId,
          timestamp: Date.now(),
          referrer: details.initiator || details.documentUrl
        }
      });
    }
  }

  isMediaUrl(url) {
    // 检查URL是否为媒体资源
    const mediaExtensions = ['.mp4', '.m3u8', '.webm', '.mkv', '.avi', '.mov'];
    const mediaDomains = ['googlevideo.com', 'youtube.com', 'youtu.be', 'tiktok.com', 'douyin.com'];

    // 检查文件扩展名
    if (mediaExtensions.some(ext => url.toLowerCase().includes(ext))) {
      return true;
    }

    // 检查特定域名的媒体URL模式
    if (mediaDomains.some(domain => url.includes(domain))) {
      // YouTube视频URL模式
      if (url.includes('googlevideo.com') && url.includes('mime=video')) {
        return true;
      }
      // TikTok视频URL模式
      if (url.includes('tiktok') && url.includes('video')) {
        return true;
      }
    }

    return false;
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'processSubtitles':
          await this.processSubtitles(request.data, sender.tab.id);
          break;
        case 'startAudioCapture':
          await this.startAudioCapture(request.data, sender.tab.id);
          break;
        case 'stopTranslation':
          await this.stopTranslation();
          break;
        case 'updateApiKeys':
          await this.updateApiKeys(request.data);
          break;
        case 'generateSubtitles':
          const subtitles = await this.generateSubtitles(request.data);
          sendResponse({subtitles});
          break;
        case 'preprocessSubtitles':
          const preprocessResults = await this.preprocessSubtitles(request.data);
          sendResponse({results: preprocessResults});
          break;
        case 'startAudioCaptureWithPreprocessing':
          await this.startAudioCaptureWithPreprocessing(request.data, sender.tab.id);
          break;
        case 'processAudioSegment':
          await this.processAudioSegment(request.data, sender.tab.id);
          break;
        case 'processDirectAudioData':
          await this.processDirectAudioData(request.data, sender.tab.id);
          break;
        case 'fallbackToMediaRecorder':
          await this.setupMediaRecorderFallback(request.data.targetLanguage);
          break;
        default:
          console.log('ChronoTranslate Background: Unknown action:', request.action);
      }
    } catch (error) {
      console.error('ChronoTranslate Background: Error handling message:', error);
      sendResponse({error: error.message});
    }
  }

  async loadApiKeys() {
    try {
      const result = await chrome.storage.local.get(['apiKeys']);
      this.apiKeys = result.apiKeys || {};
    } catch (error) {
      console.error('ChronoTranslate Background: Error loading API keys:', error);
    }
  }

  async updateApiKeys(keys) {
    this.apiKeys = {...this.apiKeys, ...keys};
    await chrome.storage.local.set({apiKeys: this.apiKeys});
  }

  async processSubtitles(data, tabId) {
    const {tasks, targetLanguage} = data;
    
    if (!tasks || tasks.length === 0) {
      throw new Error('No subtitle tasks provided');
    }

    console.log(`ChronoTranslate Background: Processing ${tasks.length} subtitle tasks`);

    try {
      // 批量翻译文本
      const translatedTexts = await this.batchTranslate(
        tasks.map(task => task.text), 
        targetLanguage
      );

      // 并行生成语音
      const results = await Promise.all(
        tasks.map(async (task, index) => {
          const translatedText = translatedTexts[index];
          const audioUrl = await this.textToSpeech(translatedText, targetLanguage);
          
          return {
            id: task.id,
            startTime: task.startTime,
            endTime: task.endTime,
            originalText: task.text,
            translatedText: translatedText,
            audioUrl: audioUrl
          };
        })
      );

      // 发送结果回content script
      chrome.tabs.sendMessage(tabId, {
        action: 'translationResult',
        data: {results}
      });

    } catch (error) {
      console.error('ChronoTranslate Background: Error processing subtitles:', error);
      chrome.tabs.sendMessage(tabId, {
        action: 'translationError',
        data: {error: error.message}
      });
    }
  }

  async startAudioCapture(data, tabId) {
    const {targetLanguage} = data;
    this.currentTabId = tabId;

    try {
      // 请求音频捕获权限
      const stream = await chrome.tabCapture.capture({
        audio: true,
        video: false
      });

      if (!stream) {
        throw new Error('Failed to capture audio stream');
      }

      this.audioStream = stream;
      this.isCapturing = true;

      // 设置音频处理
      await this.setupAudioProcessing(targetLanguage);

      console.log('ChronoTranslate Background: Audio capture started');

    } catch (error) {
      console.error('ChronoTranslate Background: Error starting audio capture:', error);
      chrome.tabs.sendMessage(tabId, {
        action: 'audioError',
        data: {error: error.message}
      });
    }
  }

  async setupAudioProcessing(targetLanguage) {
    // 创建音频上下文
    this.audioContext = new AudioContext();
    const source = this.audioContext.createMediaStreamSource(this.audioStream);

    // 使用现代的AudioWorklet或MediaRecorder
    try {
      // 尝试使用MediaRecorder进行音频捕获
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      let audioChunks = [];
      let chunkStartTime = Date.now();
      const CHUNK_DURATION = 5000; // 5秒一个音频块

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && this.isCapturing) {
          audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        if (audioChunks.length > 0) {
          const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
          this.processAudioChunk(audioBlob, chunkStartTime, targetLanguage);
        }
      };

      // 定期停止和重启录制以创建音频块
      this.recordingInterval = setInterval(() => {
        if (this.isCapturing && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop();
          audioChunks = [];
          chunkStartTime = Date.now();

          // 重新开始录制
          setTimeout(() => {
            if (this.isCapturing) {
              this.mediaRecorder.start();
            }
          }, 100);
        }
      }, CHUNK_DURATION);

      this.mediaRecorder.start();

    } catch (error) {
      console.error('ChronoTranslate Background: MediaRecorder not supported, falling back to ScriptProcessor');
      this.setupLegacyAudioProcessing(source, targetLanguage);
    }
  }

  setupLegacyAudioProcessing(source, targetLanguage) {
    // 备用方案：使用已弃用但仍然工作的ScriptProcessor
    this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);

    let audioChunks = [];
    let chunkStartTime = Date.now();
    const CHUNK_DURATION = 5000;

    this.processor.onaudioprocess = (event) => {
      if (!this.isCapturing) return;

      const inputBuffer = event.inputBuffer;
      const inputData = inputBuffer.getChannelData(0);

      audioChunks.push(new Float32Array(inputData));

      if (Date.now() - chunkStartTime >= CHUNK_DURATION) {
        // 转换为Blob格式
        const wavBlob = this.floatArrayToWav(
          this.mergeFloat32Arrays(audioChunks),
          this.audioContext.sampleRate
        );
        this.processAudioChunk(wavBlob, chunkStartTime, targetLanguage);
        audioChunks = [];
        chunkStartTime = Date.now();
      }
    };

    source.connect(this.processor);
    this.processor.connect(this.audioContext.destination);
  }

  mergeFloat32Arrays(arrays) {
    const totalLength = arrays.reduce((sum, arr) => sum + arr.length, 0);
    const result = new Float32Array(totalLength);
    let offset = 0;

    for (const array of arrays) {
      result.set(array, offset);
      offset += array.length;
    }

    return result;
  }

  async processAudioChunk(audioBlob, startTime, targetLanguage) {
    // 防止并发处理过多
    if (this.processingQueue.length >= this.maxConcurrentProcessing) {
      console.log('ChronoTranslate Background: Processing queue full, skipping chunk');
      return;
    }

    const chunkId = ++this.chunkCounter;
    this.processingQueue.push(chunkId);

    try {
      console.log(`ChronoTranslate Background: Processing audio chunk ${chunkId}`);

      // 语音识别
      const recognizedText = await this.speechToText(audioBlob);

      if (recognizedText && recognizedText.trim()) {
        console.log(`ChronoTranslate Background: Recognized text: ${recognizedText}`);

        // 翻译文本
        const translatedText = await this.translateText(recognizedText, targetLanguage);

        // 生成语音
        const audioUrl = await this.textToSpeech(translatedText, targetLanguage);

        // 发送结果到content script
        chrome.tabs.sendMessage(this.currentTabId, {
          action: 'audioReady',
          data: {
            startTime: startTime / 1000, // 转换为秒
            originalText: recognizedText,
            translatedText: translatedText,
            audioUrl: audioUrl,
            chunkId: chunkId
          }
        });
      }

    } catch (error) {
      console.error(`ChronoTranslate Background: Error processing audio chunk ${chunkId}:`, error);
    } finally {
      // 从处理队列中移除
      this.processingQueue = this.processingQueue.filter(id => id !== chunkId);
    }
  }

  floatArrayToWav(floatArray, sampleRate) {
    const length = floatArray.length;
    const buffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(buffer);
    
    // WAV文件头
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);
    
    // 音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, floatArray[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
    
    return new Blob([buffer], {type: 'audio/wav'});
  }

  async stopTranslation() {
    this.isCapturing = false;

    // 清理预处理间隔
    if (this.preprocessingInterval) {
      clearInterval(this.preprocessingInterval);
      this.preprocessingInterval = null;
    }

    // 清理录制间隔
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
      this.recordingInterval = null;
    }

    // 停止MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
      this.mediaRecorder = null;
    }

    // 停止音频流
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    // 关闭音频上下文
    if (this.audioContext) {
      await this.audioContext.close();
      this.audioContext = null;
    }

    // 断开处理器
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }

    // 清理所有缓冲区和队列
    this.audioBuffer = [];
    this.processingQueue = [];
    this.chunkCounter = 0;
    this.bufferSize = 30;

    console.log('ChronoTranslate Background: Translation and preprocessing stopped');
  }

  async generateSubtitles(data) {
    const {videoUrl, pageUrl, referrer} = data;

    try {
      console.log('ChronoTranslate Background: Generating subtitles for:', videoUrl);

      // 这里应该调用后端API来处理视频并生成字幕
      // 由于我们没有实际的后端服务，这里模拟一个简单的实现

      // 实际实现中，这里会：
      // 1. 将视频URL、页面URL和referrer发送给后端
      // 2. 后端下载视频文件
      // 3. 使用ASR技术提取音频并生成字幕
      // 4. 返回带时间戳的字幕数组

      // 模拟返回一些示例字幕
      const mockSubtitles = [
        {
          id: 'generated-0',
          text: 'This is a generated subtitle from audio recognition.',
          startTime: 0,
          endTime: 3
        },
        {
          id: 'generated-1',
          text: 'The actual implementation would use speech-to-text API.',
          startTime: 3,
          endTime: 6
        }
      ];

      // 在实际实现中，这里会调用真实的后端API
      // const response = await fetch('https://your-backend.com/api/generate-subtitles', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     videoUrl: videoUrl,
      //     pageUrl: pageUrl,
      //     referrer: referrer,
      //     userAgent: navigator.userAgent
      //   })
      // });
      //
      // if (!response.ok) {
      //   throw new Error(`Backend API error: ${response.status}`);
      // }
      //
      // const result = await response.json();
      // return result.subtitles || [];

      return mockSubtitles;

    } catch (error) {
      console.error('ChronoTranslate Background: Error generating subtitles:', error);
      return [];
    }
  }

  async preprocessSubtitles(data) {
    // 预处理所有字幕，实现零延迟播放
    const {tasks, targetLanguage, videoId} = data;

    if (!tasks || tasks.length === 0) {
      throw new Error('No subtitle tasks provided for preprocessing');
    }

    console.log(`ChronoTranslate Background: Preprocessing ${tasks.length} subtitle tasks`);

    try {
      // 批量翻译所有文本
      const translatedTexts = await this.batchTranslate(
        tasks.map(task => task.text),
        targetLanguage
      );

      // 并行生成所有语音，不等待播放时间
      const results = await Promise.all(
        tasks.map(async (task, index) => {
          const translatedText = translatedTexts[index];
          const audioUrl = await this.textToSpeech(translatedText, targetLanguage);

          return {
            id: task.id,
            startTime: task.startTime,
            endTime: task.endTime,
            originalText: task.text,
            translatedText: translatedText,
            audioUrl: audioUrl
          };
        })
      );

      console.log(`ChronoTranslate Background: Preprocessing completed for ${results.length} segments`);
      return results;

    } catch (error) {
      console.error('ChronoTranslate Background: Error preprocessing subtitles:', error);
      throw error;
    }
  }

  async startAudioCaptureWithPreprocessing(data, tabId) {
    // 启动音频捕获并开始预处理缓冲
    const {targetLanguage, bufferSize} = data;
    this.currentTabId = tabId;
    this.bufferSize = bufferSize || 30;

    try {
      // 启动音频捕获
      const stream = await chrome.tabCapture.capture({
        audio: true,
        video: false
      });

      if (!stream) {
        throw new Error('Failed to capture audio stream for preprocessing');
      }

      this.audioStream = stream;
      this.isCapturing = true;

      // 设置预处理音频管道
      await this.setupPreprocessingAudioPipeline(targetLanguage);

      console.log('ChronoTranslate Background: Audio capture with preprocessing started');

    } catch (error) {
      console.error('ChronoTranslate Background: Error starting audio preprocessing:', error);
      chrome.tabs.sendMessage(tabId, {
        action: 'audioError',
        data: {error: error.message}
      });
    }
  }

  async setupPreprocessingAudioPipeline(targetLanguage) {
    // 直接从视频缓冲区获取音频数据，而不是重新录制
    try {
      // 通知content script开始直接音频提取
      chrome.tabs.sendMessage(this.currentTabId, {
        action: 'startDirectAudioExtraction',
        data: { targetLanguage }
      });

      console.log('ChronoTranslate Background: Direct audio extraction started');

    } catch (error) {
      console.error('ChronoTranslate Background: Error setting up direct audio extraction:', error);
      // 降级到MediaRecorder方案
      await this.setupMediaRecorderFallback(targetLanguage);
    }
  }

  async setupMediaRecorderFallback(targetLanguage) {
    // 降级方案：使用MediaRecorder（仅在直接提取失败时使用）
    console.log('ChronoTranslate Background: Falling back to MediaRecorder');

    this.audioContext = new AudioContext();

    this.mediaRecorder = new MediaRecorder(this.audioStream, {
      mimeType: 'audio/webm;codecs=opus'
    });

    let audioChunks = [];
    let segmentStartTime = Date.now();
    const SEGMENT_DURATION = 3000;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0 && this.isCapturing) {
        audioChunks.push(event.data);
      }
    };

    this.mediaRecorder.onstop = () => {
      if (audioChunks.length > 0) {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        this.processAudioSegmentAsync(audioBlob, segmentStartTime, targetLanguage);
      }
    };

    this.preprocessingInterval = setInterval(() => {
      if (this.isCapturing && this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
        audioChunks = [];
        segmentStartTime = Date.now();

        setTimeout(() => {
          if (this.isCapturing) {
            this.mediaRecorder.start();
          }
        }, 100);
      }
    }, SEGMENT_DURATION);

    this.mediaRecorder.start();
  }

  async processAudioSegmentAsync(audioBlob, startTime, targetLanguage) {
    // 异步处理音频段，不阻塞主流程
    try {
      const segmentId = `segment_${startTime}`;

      // 语音识别
      const recognizedText = await this.speechToText(audioBlob);

      if (recognizedText && recognizedText.trim()) {
        // 翻译文本
        const translatedText = await this.translateText(recognizedText, targetLanguage);

        // 生成语音
        const audioUrl = await this.textToSpeech(translatedText, targetLanguage);

        // 发送到content script的缓冲区
        chrome.tabs.sendMessage(this.currentTabId, {
          action: 'audioSegmentReady',
          data: {
            segmentId: segmentId,
            startTime: startTime / 1000,
            originalText: recognizedText,
            translatedText: translatedText,
            audioUrl: audioUrl
          }
        });
      }

    } catch (error) {
      console.error('ChronoTranslate Background: Error processing audio segment:', error);
    }
  }

  async processAudioSegment(data, tabId) {
    // 处理特定时间段的音频（按需处理）
    const {startTime, duration, targetLanguage} = data;

    // 这里可以实现更精确的音频段处理
    // 目前使用连续处理模式
    console.log(`ChronoTranslate Background: Processing audio segment from ${startTime}s for ${duration}s`);
  }

  async processDirectAudioData(data, tabId) {
    // 处理直接从视频提取的音频数据
    const { audioBlob, startTime, endTime, targetLanguage } = data;

    try {
      console.log(`ChronoTranslate Background: Processing direct audio data from ${startTime}s to ${endTime}s`);

      // 语音识别
      const recognizedText = await this.speechToText(audioBlob);

      if (recognizedText && recognizedText.trim()) {
        console.log(`ChronoTranslate Background: Recognized text: ${recognizedText}`);

        // 翻译文本
        const translatedText = await this.translateText(recognizedText, targetLanguage);

        // 生成语音
        const audioUrl = await this.textToSpeech(translatedText, targetLanguage);

        // 发送结果到content script
        chrome.tabs.sendMessage(tabId, {
          action: 'audioSegmentReady',
          data: {
            segmentId: `direct_${Date.now()}`,
            startTime: startTime,
            endTime: endTime,
            originalText: recognizedText,
            translatedText: translatedText,
            audioUrl: audioUrl
          }
        });

        console.log(`ChronoTranslate Background: Direct audio processing completed for ${startTime}s-${endTime}s`);

      } else {
        console.log('ChronoTranslate Background: No speech detected in direct audio data');
      }

    } catch (error) {
      console.error('ChronoTranslate Background: Error processing direct audio data:', error);
    }
  }

  // API调用方法
  async batchTranslate(texts, targetLanguage) {
    if (!texts || texts.length === 0) return [];

    try {
      // 使用Google Translate API
      if (this.apiKeys.googleTranslate) {
        return await this.googleTranslateBatch(texts, targetLanguage);
      }

      // 使用OpenAI API作为备选
      if (this.apiKeys.openai) {
        return await this.openaiTranslateBatch(texts, targetLanguage);
      }

      throw new Error('No translation API key configured');
    } catch (error) {
      console.error('ChronoTranslate Background: Translation error:', error);
      throw error;
    }
  }

  async translateText(text, targetLanguage) {
    const results = await this.batchTranslate([text], targetLanguage);
    return results[0] || text;
  }

  async googleTranslateBatch(texts, targetLanguage) {
    const apiKey = this.apiKeys.googleTranslate;
    const url = `https://translation.googleapis.com/language/translate/v2?key=${apiKey}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: texts,
        target: targetLanguage,
        format: 'text'
      })
    });

    if (!response.ok) {
      throw new Error(`Google Translate API error: ${response.status}`);
    }

    const data = await response.json();
    return data.data.translations.map(t => t.translatedText);
  }

  async openaiTranslateBatch(texts, targetLanguage) {
    const apiKey = this.apiKeys.openai;
    const languageNames = {
      'zh-CN': '简体中文',
      'zh-TW': '繁体中文',
      'ja': '日语',
      'ko': '韩语',
      'es': '西班牙语',
      'fr': '法语',
      'de': '德语',
      'ru': '俄语'
    };

    const targetLangName = languageNames[targetLanguage] || targetLanguage;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{
          role: 'user',
          content: `请将以下文本翻译成${targetLangName}，保持原意，只返回翻译结果，多个文本用换行符分隔：\n\n${texts.join('\n')}`
        }],
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const translatedText = data.choices[0].message.content.trim();
    return translatedText.split('\n').filter(t => t.trim());
  }

  async textToSpeech(text, language) {
    try {
      // 使用Google Text-to-Speech API
      if (this.apiKeys.googleTTS) {
        return await this.googleTextToSpeech(text, language);
      }

      // 使用OpenAI TTS API作为备选
      if (this.apiKeys.openai) {
        return await this.openaiTextToSpeech(text, language);
      }

      throw new Error('No TTS API key configured');
    } catch (error) {
      console.error('ChronoTranslate Background: TTS error:', error);
      throw error;
    }
  }

  async googleTextToSpeech(text, language) {
    const apiKey = this.apiKeys.googleTTS;
    const url = `https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: {text: text},
        voice: {
          languageCode: language,
          ssmlGender: 'NEUTRAL'
        },
        audioConfig: {
          audioEncoding: 'MP3'
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Google TTS API error: ${response.status}`);
    }

    const data = await response.json();
    const audioContent = data.audioContent;

    // 将base64音频转换为blob URL
    const audioBlob = new Blob([
      Uint8Array.from(atob(audioContent), c => c.charCodeAt(0))
    ], {type: 'audio/mp3'});

    return URL.createObjectURL(audioBlob);
  }

  async openaiTextToSpeech(text, _language) {
    const apiKey = this.apiKeys.openai;

    const response = await fetch('https://api.openai.com/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'tts-1',
        input: text,
        voice: 'alloy',
        response_format: 'mp3'
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI TTS API error: ${response.status}`);
    }

    const audioBlob = await response.blob();
    return URL.createObjectURL(audioBlob);
  }

  async speechToText(audioBlob) {
    try {
      // 使用OpenAI Whisper API
      if (this.apiKeys.openai) {
        return await this.openaiSpeechToText(audioBlob);
      }

      // 使用Google Speech-to-Text API作为备选
      if (this.apiKeys.googleSTT) {
        return await this.googleSpeechToText(audioBlob);
      }

      throw new Error('No STT API key configured');
    } catch (error) {
      console.error('ChronoTranslate Background: STT error:', error);
      throw error;
    }
  }

  async openaiSpeechToText(audioBlob) {
    const apiKey = this.apiKeys.openai;
    const formData = new FormData();
    formData.append('file', audioBlob, 'audio.wav');
    formData.append('model', 'whisper-1');

    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`OpenAI Whisper API error: ${response.status}`);
    }

    const data = await response.json();
    return data.text;
  }

  async googleSpeechToText(audioBlob) {
    const apiKey = this.apiKeys.googleSTT;

    // 将音频转换为base64
    const arrayBuffer = await audioBlob.arrayBuffer();
    const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

    const response = await fetch(`https://speech.googleapis.com/v1/speech:recognize?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        config: {
          encoding: 'WEBM_OPUS',
          sampleRateHertz: 48000,
          languageCode: 'en-US'
        },
        audio: {
          content: base64Audio
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Google STT API error: ${response.status}`);
    }

    const data = await response.json();
    if (data.results && data.results.length > 0) {
      return data.results[0].alternatives[0].transcript;
    }

    return '';
  }
}

// 初始化后台服务
const chronoTranslateBackground = new ChronoTranslateBackground();
