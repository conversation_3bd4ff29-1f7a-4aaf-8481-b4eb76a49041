// content.js - YouTube页面内容脚本
class ChronoTranslate {
  constructor() {
    this.isTranslating = false;
    this.currentMode = null; // 'subtitle' or 'audio'
    this.video = null;
    this.controlPanel = null;
    this.audioQueue = [];
    this.currentAudio = null;
    this.translationResults = [];
    this.targetLanguage = 'zh-CN';
    this.videoId = null;
    this.cache = new Map(); // 缓存翻译结果
    this.syncBuffer = 0.5; // 同步缓冲时间（秒）
    this.lastPlayedIndex = -1;

    // 预处理和缓冲相关
    this.isPreprocessing = false;
    this.preprocessBuffer = new Map(); // 预处理缓冲区
    this.bufferSize = 30; // 预处理30秒的内容
    this.processingQueue = [];
    this.maxConcurrentProcessing = 3;

    this.init();
  }

  init() {
    // 等待页面加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    // 获取视频ID
    this.extractVideoId();

    // 查找视频元素
    this.findVideoElement();

    // 创建控制面板
    this.createControlPanel();

    // 监听消息
    this.setupMessageListener();

    // 监听页面变化（YouTube SPA导航）
    this.setupNavigationListener();
  }

  extractVideoId() {
    const urlParams = new URLSearchParams(window.location.search);
    this.videoId = urlParams.get('v');
  }

  findVideoElement() {
    const video = document.querySelector('video');
    if (video) {
      this.video = video;
      console.log('ChronoTranslate: Video element found');
    } else {
      // 如果没找到，延迟重试
      setTimeout(() => this.findVideoElement(), 1000);
    }
  }

  createControlPanel() {
    // 避免重复创建
    if (document.getElementById('chrono-translate-panel')) {
      return;
    }

    const panel = document.createElement('div');
    panel.id = 'chrono-translate-panel';
    panel.className = 'chrono-translate-panel';
    
    panel.innerHTML = `
      <div class="ct-header">
        <span class="ct-logo">🌐 ChronoTranslate</span>
        <div class="ct-status" id="ct-status">准备就绪</div>
      </div>
      <div class="ct-progress" id="ct-progress" style="display: none;">
        <div class="ct-progress-bar" id="ct-progress-bar"></div>
      </div>
      <div class="ct-controls">
        <button id="ct-toggle" class="ct-btn ct-btn-primary">开始翻译</button>
        <select id="ct-language" class="ct-select">
          <option value="zh-CN">中文 (简体)</option>
          <option value="zh-TW">中文 (繁體)</option>
          <option value="ja">日本語</option>
          <option value="ko">한국어</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
          <option value="ru">Русский</option>
        </select>
        <div class="ct-mode-info" id="ct-mode-info">检测中...</div>
        <div class="ct-audio-visualizer" id="ct-visualizer" style="display: none;">
          <div class="ct-audio-bar"></div>
          <div class="ct-audio-bar"></div>
          <div class="ct-audio-bar"></div>
          <div class="ct-audio-bar"></div>
          <div class="ct-audio-bar"></div>
        </div>
      </div>
      <div class="ct-stats" id="ct-stats" style="display: none;">
        <div class="ct-stat-item">
          <span>已翻译:</span>
          <span class="ct-stat-value" id="ct-translated-count">0</span>
        </div>
        <div class="ct-stat-item">
          <span>延迟:</span>
          <span class="ct-stat-value" id="ct-delay">0s</span>
        </div>
        <div class="ct-stat-item">
          <span>模式:</span>
          <span class="ct-stat-value" id="ct-current-mode">-</span>
        </div>
      </div>
      <div class="ct-advanced">
        <button class="ct-advanced-toggle" id="ct-advanced-toggle">高级设置</button>
        <div class="ct-advanced-content" id="ct-advanced-content">
          <div class="ct-slider">
            <label>同步缓冲 (秒): <span id="ct-buffer-value">0.5</span></label>
            <input type="range" id="ct-buffer-slider" min="0" max="2" step="0.1" value="0.5">
          </div>
        </div>
      </div>
    `;

    // 插入到视频播放器下方
    const playerContainer = document.querySelector('#movie_player') || 
                           document.querySelector('.html5-video-player') ||
                           document.querySelector('#player-container');
    
    if (playerContainer) {
      playerContainer.parentNode.insertBefore(panel, playerContainer.nextSibling);
      this.controlPanel = panel;
      this.setupControlPanelEvents();
    } else {
      // 如果没找到合适位置，延迟重试
      setTimeout(() => this.createControlPanel(), 1000);
    }
  }

  setupControlPanelEvents() {
    const toggleBtn = document.getElementById('ct-toggle');
    const languageSelect = document.getElementById('ct-language');
    const advancedToggle = document.getElementById('ct-advanced-toggle');
    const bufferSlider = document.getElementById('ct-buffer-slider');
    const bufferValue = document.getElementById('ct-buffer-value');

    toggleBtn.addEventListener('click', () => this.toggleTranslation());

    languageSelect.addEventListener('change', (e) => {
      this.targetLanguage = e.target.value;
    });

    advancedToggle.addEventListener('click', () => {
      const content = document.getElementById('ct-advanced-content');
      content.classList.toggle('show');
      advancedToggle.textContent = content.classList.contains('show') ? '隐藏高级设置' : '高级设置';
    });

    bufferSlider.addEventListener('input', (e) => {
      this.syncBuffer = parseFloat(e.target.value);
      bufferValue.textContent = this.syncBuffer.toFixed(1);
    });
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'getStatus':
          sendResponse({isTranslating: this.isTranslating});
          break;
        case 'toggleTranslation':
          this.toggleTranslation();
          sendResponse({isTranslating: this.isTranslating});
          break;
        case 'translationResult':
          this.handleTranslationResult(request.data);
          break;
        case 'audioReady':
          this.handleAudioReady(request.data);
          break;
        case 'audioSegmentReady':
          this.handleAudioSegmentReady(request.data);
          break;
        case 'startDirectAudioExtraction':
          this.startDirectAudioExtraction(request.data);
          break;
      }
    });
  }

  setupNavigationListener() {
    // 监听YouTube的SPA导航
    let lastUrl = location.href;
    new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        lastUrl = url;
        if (url.includes('/watch')) {
          // 页面切换到视频页面，重新初始化
          setTimeout(() => {
            this.findVideoElement();
            this.createControlPanel();
          }, 1000);
        }
      }
    }).observe(document, {subtree: true, childList: true});
  }

  async toggleTranslation() {
    if (this.isTranslating) {
      this.stopTranslation();
    } else {
      await this.startTranslation();
    }
  }

  async startTranslation() {
    if (!this.video) {
      this.updateStatus('错误：未找到视频元素', 'error');
      return;
    }

    this.updateStatus('正在检测翻译模式...', 'loading');

    // 检测字幕模式
    const subtitleMode = await this.detectSubtitleMode();

    if (subtitleMode) {
      this.currentMode = 'subtitle';
      this.updateModeInfo('模式：字幕翻译 (预处理中)');
      await this.startSubtitleModeWithPreprocessing();
    } else {
      this.currentMode = 'audio';
      this.updateModeInfo('模式：音频翻译 (预处理中)');
      await this.startAudioModeWithPreprocessing();
    }
  }

  async startSubtitleModeWithPreprocessing() {
    this.isTranslating = true;
    this.isPreprocessing = true;
    this.updateStatus('正在预处理字幕...', 'loading');
    document.getElementById('ct-toggle').textContent = '停止翻译';

    try {
      // 检查缓存
      const cacheKey = `${this.videoId}_${this.targetLanguage}`;
      if (this.cache.has(cacheKey)) {
        console.log('ChronoTranslate: Using cached translation results');
        this.translationResults = this.cache.get(cacheKey);
        this.isPreprocessing = false;
        this.video.muted = true;
        this.setupVideoTimeListener();
        this.updateStatus('翻译就绪 (缓存)', 'active');
        this.updateModeInfo('模式：字幕翻译 (无延迟)');
        this.updateStats();
        return;
      }

      // 提取所有字幕
      const subtitleTasks = await this.extractSubtitles();

      if (subtitleTasks.length === 0) {
        throw new Error('无法提取字幕数据');
      }

      // 立即开始预处理所有字幕
      this.updateStatus('正在批量翻译字幕...', 'loading');
      await this.preprocessAllSubtitles(subtitleTasks);

      // 预处理完成，准备播放
      this.isPreprocessing = false;
      this.video.muted = true;
      this.setupVideoTimeListener();
      this.updateStatus('翻译就绪 (无延迟)', 'active');
      this.updateModeInfo('模式：字幕翻译 (无延迟)');
      this.updateStats();

    } catch (error) {
      console.error('ChronoTranslate: Error in subtitle preprocessing:', error);
      this.updateStatus('错误：' + error.message, 'error');
      this.showNotification('字幕预处理失败：' + error.message, 'error');
      this.stopTranslation();
    }
  }

  async startAudioModeWithPreprocessing() {
    this.isTranslating = true;
    this.isPreprocessing = true;
    this.updateStatus('正在启动音频预处理...', 'loading');
    document.getElementById('ct-toggle').textContent = '停止翻译';

    try {
      // 启动音频捕获和预处理
      chrome.runtime.sendMessage({
        action: 'startAudioCaptureWithPreprocessing',
        data: {
          targetLanguage: this.targetLanguage,
          bufferSize: this.bufferSize
        }
      });

      // 静音原视频并开始监听
      this.video.muted = true;
      this.setupVideoTimeListener();

      this.updateStatus('音频预处理进行中', 'active');
      this.updateModeInfo('模式：音频翻译 (缓冲中)');
      this.showVisualizer(true);
      this.updateStats();

    } catch (error) {
      console.error('ChronoTranslate: Error in audio preprocessing:', error);
      this.updateStatus('错误：' + error.message, 'error');
      this.showNotification('音频预处理失败：' + error.message, 'error');
      this.stopTranslation();
    }
  }

  stopTranslation() {
    this.isTranslating = false;
    this.isPreprocessing = false;
    this.currentMode = null;

    // 恢复原始音频
    if (this.video) {
      this.video.muted = false;
    }

    // 停止当前播放的翻译音频
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio = null;
    }

    // 清空所有缓冲区和队列
    this.audioQueue = [];
    this.translationResults = [];
    this.preprocessBuffer.clear();
    this.processingQueue = [];
    this.lastPlayedIndex = -1;

    // 更新UI
    this.updateStatus('已停止', 'stopped');
    this.updateModeInfo('');
    this.showVisualizer(false);
    this.updateStats();
    document.getElementById('ct-toggle').textContent = '开始翻译';

    // 通知background script停止
    chrome.runtime.sendMessage({action: 'stopTranslation'});
  }

  async detectSubtitleMode() {
    if (!this.video) return false;

    try {
      const textTracks = this.video.textTracks;
      for (let i = 0; i < textTracks.length; i++) {
        const track = textTracks[i];
        if (track.kind === 'subtitles' || track.kind === 'captions') {
          // 尝试激活字幕轨道
          track.mode = 'showing';

          // 等待字幕加载
          await new Promise(resolve => setTimeout(resolve, 1000));

          if (track.cues && track.cues.length > 0) {
            console.log('ChronoTranslate: Subtitles detected, using subtitle mode');
            return true;
          }
        }
      }
    } catch (error) {
      console.error('ChronoTranslate: Error detecting subtitles:', error);
    }

    console.log('ChronoTranslate: No subtitles found, will use audio mode');
    return false;
  }

  async startSubtitleMode() {
    this.isTranslating = true;
    this.updateStatus('正在检查缓存...', 'loading');
    document.getElementById('ct-toggle').textContent = '停止翻译';

    try {
      // 检查缓存
      const cacheKey = `${this.videoId}_${this.targetLanguage}`;
      if (this.cache.has(cacheKey)) {
        console.log('ChronoTranslate: Using cached translation results');
        this.translationResults = this.cache.get(cacheKey);
        this.video.muted = true;
        this.setupVideoTimeListener();
        this.updateStatus('翻译进行中 (缓存)', 'active');
        return;
      }

      this.updateStatus('正在提取字幕...', 'loading');

      // 提取字幕数据
      const subtitleTasks = await this.extractSubtitles();

      if (subtitleTasks.length === 0) {
        throw new Error('无法提取字幕数据');
      }

      this.updateStatus('正在翻译字幕...', 'loading');

      // 发送给background script处理
      chrome.runtime.sendMessage({
        action: 'processSubtitles',
        data: {
          tasks: subtitleTasks,
          targetLanguage: this.targetLanguage,
          videoId: this.videoId
        }
      });

      // 静音原视频
      this.video.muted = true;

      // 开始监听视频时间更新
      this.setupVideoTimeListener();

      this.updateStatus('翻译进行中', 'active');
      this.updateStats();

    } catch (error) {
      console.error('ChronoTranslate: Error in subtitle mode:', error);
      this.updateStatus('错误：' + error.message, 'error');
      this.showNotification('字幕翻译启动失败：' + error.message, 'error');
      this.stopTranslation();
    }
  }

  async startAudioMode() {
    this.isTranslating = true;
    this.updateStatus('正在启动音频捕获...', 'loading');
    document.getElementById('ct-toggle').textContent = '停止翻译';

    try {
      // 请求background script开始音频捕获
      chrome.runtime.sendMessage({
        action: 'startAudioCapture',
        data: {
          targetLanguage: this.targetLanguage
        }
      });

      // 静音原视频
      this.video.muted = true;

      this.updateStatus('音频翻译进行中', 'active');
      this.showVisualizer(true);
      this.updateStats();

    } catch (error) {
      console.error('ChronoTranslate: Error in audio mode:', error);
      this.updateStatus('错误：' + error.message, 'error');
      this.showNotification('音频翻译启动失败：' + error.message, 'error');
      this.stopTranslation();
    }
  }

  async extractSubtitles() {
    const tasks = [];

    // 首先尝试从HTML5 video元素获取字幕
    if (this.video && this.video.textTracks) {
      for (let i = 0; i < this.video.textTracks.length; i++) {
        const track = this.video.textTracks[i];

        if ((track.kind === 'subtitles' || track.kind === 'captions') &&
            track.cues && track.cues.length > 0) {

          for (let j = 0; j < track.cues.length; j++) {
            const cue = track.cues[j];
            tasks.push({
              id: `${i}-${j}`,
              text: cue.text,
              startTime: cue.startTime,
              endTime: cue.endTime
            });
          }
          break; // 只处理第一个可用的字幕轨道
        }
      }
    }

    // 如果没有找到字幕，尝试网站特定的字幕获取方法
    if (tasks.length === 0) {
      const siteSpecificSubtitles = await this.getSiteSpecificSubtitles();
      tasks.push(...siteSpecificSubtitles);
    }

    return tasks;
  }

  async getSiteSpecificSubtitles() {
    const url = window.location.href;
    const tasks = [];

    try {
      if (url.includes('youtube.com')) {
        return await this.getYouTubeSubtitles();
      } else if (url.includes('bilibili.com')) {
        return await this.getBilibiliSubtitles();
      } else {
        // 对于其他网站，请求后端生成字幕
        return await this.getCommonSubtitles();
      }
    } catch (error) {
      console.error('ChronoTranslate: Error getting site-specific subtitles:', error);
      return tasks;
    }
  }

  async getYouTubeSubtitles() {
    // YouTube字幕获取逻辑
    const tasks = [];

    try {
      // 尝试从YouTube的内部API获取字幕
      const videoId = new URLSearchParams(window.location.search).get('v');
      if (!videoId) return tasks;

      // 这里可以调用YouTube的字幕API
      // 由于YouTube的API需要特殊处理，这里先返回空数组
      console.log('ChronoTranslate: YouTube subtitle extraction not implemented yet');

    } catch (error) {
      console.error('ChronoTranslate: YouTube subtitle extraction error:', error);
    }

    return tasks;
  }

  async getBilibiliSubtitles() {
    // B站字幕获取逻辑
    const tasks = [];

    try {
      // B站的字幕通常在页面的JavaScript变量中
      const scriptTags = document.querySelectorAll('script');
      for (const script of scriptTags) {
        if (script.textContent.includes('subtitle')) {
          // 解析B站的字幕数据
          console.log('ChronoTranslate: Bilibili subtitle extraction not implemented yet');
          break;
        }
      }
    } catch (error) {
      console.error('ChronoTranslate: Bilibili subtitle extraction error:', error);
    }

    return tasks;
  }

  async getCommonSubtitles() {
    // 对于通用网站，请求后端生成字幕
    const tasks = [];

    try {
      // 获取嗅探到的媒体URL
      const result = await chrome.storage.session.get([`media_${this.currentTabId}`]);
      const mediaInfo = result[`media_${this.currentTabId}`];

      if (mediaInfo && mediaInfo.videoUrl) {
        // 请求后端处理视频并生成字幕
        const response = await chrome.runtime.sendMessage({
          action: 'generateSubtitles',
          data: {
            videoUrl: mediaInfo.videoUrl,
            pageUrl: window.location.href,
            referrer: mediaInfo.referrer
          }
        });

        if (response && response.subtitles) {
          tasks.push(...response.subtitles);
        }
      }
    } catch (error) {
      console.error('ChronoTranslate: Common subtitle generation error:', error);
    }

    return tasks;
  }

  setupVideoTimeListener() {
    if (!this.video) return;

    this.video.addEventListener('timeupdate', () => {
      this.syncAudioPlayback();
    });
  }

  syncAudioPlayback() {
    if (!this.video || !this.isTranslating) return;

    const currentTime = this.video.currentTime;

    // 如果还在预处理中，显示缓冲状态
    if (this.isPreprocessing) {
      this.updateModeInfo(`模式：${this.currentMode === 'subtitle' ? '字幕' : '音频'}翻译 (预处理中...)`);
      return;
    }

    // 对于音频模式，启动连续预处理
    if (this.currentMode === 'audio') {
      this.startContinuousAudioPreprocessing();
    }

    // 查找当前时间对应的翻译音频
    let currentResult = null;
    let currentIndex = -1;

    // 优先从预处理结果中查找
    if (this.translationResults.length > 0) {
      currentIndex = this.translationResults.findIndex(result =>
        currentTime >= (result.startTime - this.syncBuffer) &&
        currentTime <= (result.endTime + this.syncBuffer)
      );

      if (currentIndex !== -1) {
        currentResult = this.translationResults[currentIndex];
      }
    }

    // 如果预处理结果中没有，检查实时缓冲区（音频模式）
    if (!currentResult && this.currentMode === 'audio') {
      const bufferKey = Math.floor(currentTime);
      if (this.preprocessBuffer.has(bufferKey)) {
        currentResult = this.preprocessBuffer.get(bufferKey);
        currentIndex = bufferKey;
      }
    }

    // 播放找到的音频
    if (currentResult && currentIndex !== this.lastPlayedIndex) {
      if (currentResult.audioUrl) {
        this.playTranslationAudio(currentResult, currentTime);
        this.lastPlayedIndex = currentIndex;

        // 更新状态为无延迟
        if (!this.isPreprocessing) {
          this.updateModeInfo(`模式：${this.currentMode === 'subtitle' ? '字幕' : '音频'}翻译 (无延迟)`);
        }
      }
    } else if (!currentResult && this.currentAudio) {
      // 当前时间没有对应的翻译音频，停止播放
      this.currentAudio.pause();
      this.currentAudio = null;
      this.lastPlayedIndex = -1;
    }
  }

  playTranslationAudio(result, currentVideoTime) {
    // 停止当前音频
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.removeEventListener('ended', this.currentAudioEndHandler);
    }

    // 创建新的音频元素
    this.currentAudio = new Audio(result.audioUrl);
    this.currentAudio.volume = 1.0;

    // 计算精确的音频播放偏移时间
    const videoTime = currentVideoTime || this.video.currentTime;
    const audioOffset = Math.max(0, videoTime - result.startTime);
    const audioDuration = result.endTime - result.startTime;

    // 只有在合理的时间范围内才设置偏移
    if (audioOffset < audioDuration) {
      this.currentAudio.currentTime = audioOffset;
    }

    // 设置音频结束时的清理处理器
    this.currentAudioEndHandler = () => {
      if (this.currentAudio) {
        this.currentAudio = null;
        this.lastPlayedIndex = -1;
      }
    };
    this.currentAudio.addEventListener('ended', this.currentAudioEndHandler);

    // 监听音频加载完成事件，确保同步播放
    this.currentAudio.addEventListener('canplaythrough', () => {
      // 再次检查视频时间，确保同步
      const currentTime = this.video.currentTime;
      if (currentTime >= result.startTime && currentTime <= result.endTime) {
        const newOffset = Math.max(0, currentTime - result.startTime);
        if (Math.abs(this.currentAudio.currentTime - newOffset) > 0.1) {
          this.currentAudio.currentTime = newOffset;
        }
      }
    }, { once: true });

    // 播放音频
    this.currentAudio.play().catch(error => {
      console.error('ChronoTranslate: Error playing audio:', error);
      // 如果播放失败，尝试预加载下一个音频
      this.preloadNextAudio();
    });

    // 记录播放统计
    this.updatePlaybackStats(result);
  }

  preloadNextAudio() {
    // 预加载下一个音频片段以减少延迟
    const currentIndex = this.lastPlayedIndex;
    const nextIndex = currentIndex + 1;

    if (nextIndex < this.translationResults.length) {
      const nextResult = this.translationResults[nextIndex];
      if (nextResult && nextResult.audioUrl) {
        // 创建隐藏的音频元素进行预加载
        const preloadAudio = new Audio(nextResult.audioUrl);
        preloadAudio.preload = 'auto';
        preloadAudio.volume = 0; // 静音预加载

        // 预加载完成后移除元素
        preloadAudio.addEventListener('canplaythrough', () => {
          setTimeout(() => preloadAudio.remove(), 1000);
        }, { once: true });
      }
    }
  }

  updatePlaybackStats(result) {
    // 更新播放统计信息
    const now = Date.now();
    const delay = now - (this.video.currentTime * 1000 + this.video.currentTime * 1000);

    // 更新延迟显示
    const delayElement = document.getElementById('ct-delay');
    if (delayElement) {
      delayElement.textContent = Math.abs(delay / 1000).toFixed(1) + 's';
    }

    // 记录调试信息
    if (window.ChronoTranslateDebugger) {
      window.ChronoTranslateDebugger.log('info', 'Audio Playback', {
        subtitleId: result.id,
        videoTime: this.video.currentTime,
        audioStart: result.startTime,
        audioEnd: result.endTime,
        estimatedDelay: delay
      });
    }
  }

  handleTranslationResult(data) {
    // 接收来自background script的翻译结果
    this.translationResults = data.results || [];
    console.log('ChronoTranslate: Received translation results:', this.translationResults.length);

    // 缓存结果
    if (this.videoId && this.targetLanguage) {
      const cacheKey = `${this.videoId}_${this.targetLanguage}`;
      this.cache.set(cacheKey, this.translationResults);

      // 限制缓存大小（最多保存10个视频的翻译结果）
      if (this.cache.size > 10) {
        const firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
      }
    }
  }

  handleAudioReady(data) {
    // 处理实时音频翻译结果（音频模式）
    if (data.audioUrl && data.startTime !== undefined) {
      this.audioQueue.push(data);
      this.processAudioQueue();
    }
  }

  handleAudioSegmentReady(data) {
    // 处理预处理的音频段
    const {segmentId, startTime, audioUrl} = data;

    if (audioUrl && startTime !== undefined) {
      // 将音频段添加到预处理缓冲区
      const bufferKey = Math.floor(startTime);
      this.preprocessBuffer.set(bufferKey, {
        segmentId: segmentId,
        startTime: startTime,
        endTime: startTime + 3, // 假设每段3秒
        audioUrl: audioUrl,
        originalText: data.originalText,
        translatedText: data.translatedText
      });

      // 限制缓冲区大小
      if (this.preprocessBuffer.size > 100) {
        const oldestKey = Math.min(...this.preprocessBuffer.keys());
        this.preprocessBuffer.delete(oldestKey);
      }

      // 如果这是第一个音频段，标记预处理完成
      if (this.isPreprocessing && this.preprocessBuffer.size >= 3) {
        this.isPreprocessing = false;
        this.updateModeInfo('模式：音频翻译 (缓冲就绪)');
        this.showNotification('音频预处理缓冲就绪', 'success');
      }

      console.log(`ChronoTranslate: Audio segment buffered for time ${startTime}s`);
    }
  }

  processAudioQueue() {
    // 处理音频队列，按时间顺序播放
    if (this.audioQueue.length === 0) return;

    const currentTime = this.video ? this.video.currentTime : 0;

    // 查找应该播放的音频
    const audioToPlay = this.audioQueue.find(audio =>
      Math.abs(audio.startTime - currentTime) < 1.0 // 1秒容差
    );

    if (audioToPlay) {
      this.playTranslationAudio(audioToPlay);
      // 从队列中移除已播放的音频
      this.audioQueue = this.audioQueue.filter(audio => audio !== audioToPlay);
    }
  }

  updateStatus(message, type = 'normal') {
    const statusElement = document.getElementById('ct-status');
    if (statusElement) {
      statusElement.textContent = message;
      statusElement.className = `ct-status ct-status-${type}`;
    }

    // 显示/隐藏进度条
    const progressElement = document.getElementById('ct-progress');
    const progressBar = document.getElementById('ct-progress-bar');

    if (type === 'loading') {
      progressElement.style.display = 'block';
      progressBar.className = 'ct-progress-bar indeterminate';
    } else {
      progressElement.style.display = 'none';
    }
  }

  updateModeInfo(message) {
    const modeElement = document.getElementById('ct-mode-info');
    if (modeElement) {
      modeElement.textContent = message;
    }

    // 更新统计信息
    const currentModeElement = document.getElementById('ct-current-mode');
    if (currentModeElement) {
      currentModeElement.textContent = this.currentMode === 'subtitle' ? '字幕' :
                                      this.currentMode === 'audio' ? '音频' : '-';
    }
  }

  updateStats() {
    const statsElement = document.getElementById('ct-stats');
    const translatedCountElement = document.getElementById('ct-translated-count');
    const delayElement = document.getElementById('ct-delay');

    if (this.isTranslating) {
      statsElement.style.display = 'flex';

      if (translatedCountElement) {
        translatedCountElement.textContent = this.translationResults.length;
      }

      if (delayElement) {
        // 简单的延迟估算
        const avgDelay = this.currentMode === 'subtitle' ? '1-2s' : '3-5s';
        delayElement.textContent = avgDelay;
      }
    } else {
      statsElement.style.display = 'none';
    }
  }

  showVisualizer(show = true) {
    const visualizer = document.getElementById('ct-visualizer');
    if (visualizer) {
      visualizer.style.display = show && this.currentMode === 'audio' ? 'flex' : 'none';
    }
  }

  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `ct-notification ${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => notification.classList.add('show'), 100);

    // 自动隐藏
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
  }

  async preprocessAllSubtitles(subtitleTasks) {
    // 批量预处理所有字幕，实现零延迟播放
    try {
      this.updateStatus(`正在处理 ${subtitleTasks.length} 条字幕...`, 'loading');

      // 发送给background script进行批量处理
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: 'preprocessSubtitles',
          data: {
            tasks: subtitleTasks,
            targetLanguage: this.targetLanguage,
            videoId: this.videoId
          }
        }, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (response && response.results) {
        this.translationResults = response.results;

        // 缓存结果
        if (this.videoId && this.targetLanguage) {
          const cacheKey = `${this.videoId}_${this.targetLanguage}`;
          this.cache.set(cacheKey, this.translationResults);

          // 限制缓存大小
          if (this.cache.size > 10) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
          }
        }

        console.log(`ChronoTranslate: Preprocessed ${this.translationResults.length} subtitle segments`);
        this.showNotification(`预处理完成：${this.translationResults.length} 条字幕`, 'success');
      } else {
        throw new Error('预处理返回空结果');
      }

    } catch (error) {
      console.error('ChronoTranslate: Preprocessing error:', error);
      throw error;
    }
  }

  startContinuousAudioPreprocessing() {
    // 连续音频预处理，维持缓冲区
    if (!this.isTranslating || this.currentMode !== 'audio') return;

    const currentTime = this.video.currentTime;
    const bufferEndTime = currentTime + this.bufferSize;

    // 检查缓冲区是否需要补充
    const needsBuffering = !this.hasBufferedContent(currentTime, bufferEndTime);

    if (needsBuffering && this.processingQueue.length < this.maxConcurrentProcessing) {
      // 请求处理接下来的音频段
      chrome.runtime.sendMessage({
        action: 'processAudioSegment',
        data: {
          startTime: currentTime,
          duration: this.bufferSize,
          targetLanguage: this.targetLanguage
        }
      });
    }
  }

  hasBufferedContent(startTime, endTime) {
    // 检查指定时间范围是否已有缓冲内容
    for (let time = startTime; time < endTime; time += 1) {
      if (!this.preprocessBuffer.has(Math.floor(time))) {
        return false;
      }
    }
    return true;
  }

  startDirectAudioExtraction(data) {
    // 直接从视频元素提取音频数据，而不是重新录制
    const { targetLanguage } = data;

    if (!this.video) {
      console.error('ChronoTranslate: No video element found for direct audio extraction');
      return;
    }

    try {
      // 创建音频上下文来处理视频的音频
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // 直接从video元素创建音频源
      this.audioSource = this.audioContext.createMediaElementSource(this.video);

      // 创建分析器节点来获取音频数据
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;

      // 创建增益节点来控制音量
      this.gainNode = this.audioContext.createGain();

      // 连接音频节点
      this.audioSource.connect(this.analyser);
      this.analyser.connect(this.gainNode);
      this.gainNode.connect(this.audioContext.destination);

      // 开始直接音频处理
      this.startDirectAudioProcessing(targetLanguage);

      console.log('ChronoTranslate: Direct audio extraction started');

    } catch (error) {
      console.error('ChronoTranslate: Error setting up direct audio extraction:', error);
      // 通知background script降级到MediaRecorder
      chrome.runtime.sendMessage({
        action: 'fallbackToMediaRecorder',
        data: { targetLanguage }
      });
    }
  }

  startDirectAudioProcessing(targetLanguage) {
    // 直接处理视频音频数据
    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const audioBuffer = [];
    let lastProcessTime = 0;
    const PROCESS_INTERVAL = 3000; // 3秒处理一次

    const processAudioData = () => {
      if (!this.isTranslating || this.currentMode !== 'audio') {
        return;
      }

      const currentTime = this.video.currentTime * 1000; // 转换为毫秒

      // 获取当前音频数据
      this.analyser.getByteFrequencyData(dataArray);

      // 将音频数据添加到缓冲区
      audioBuffer.push({
        data: new Uint8Array(dataArray),
        timestamp: currentTime,
        videoTime: this.video.currentTime
      });

      // 每3秒处理一次音频数据
      if (currentTime - lastProcessTime >= PROCESS_INTERVAL) {
        this.processDirectAudioBuffer(audioBuffer.splice(0), targetLanguage);
        lastProcessTime = currentTime;
      }

      // 继续处理
      requestAnimationFrame(processAudioData);
    };

    // 开始音频数据处理循环
    processAudioData();
  }

  async processDirectAudioBuffer(audioBuffer, targetLanguage) {
    // 处理直接提取的音频数据
    if (audioBuffer.length === 0) return;

    try {
      // 将音频数据转换为可处理的格式
      const audioData = this.convertAudioBufferToBlob(audioBuffer);

      if (audioData) {
        // 发送给background script进行ASR处理
        chrome.runtime.sendMessage({
          action: 'processDirectAudioData',
          data: {
            audioBlob: audioData,
            startTime: audioBuffer[0].videoTime,
            endTime: audioBuffer[audioBuffer.length - 1].videoTime,
            targetLanguage: targetLanguage
          }
        });
      }

    } catch (error) {
      console.error('ChronoTranslate: Error processing direct audio buffer:', error);
    }
  }

  convertAudioBufferToBlob(audioBuffer) {
    // 将音频数据数组转换为Blob
    try {
      // 这里需要将Uint8Array音频数据转换为WAV格式
      // 简化实现：创建一个基本的WAV文件
      const sampleRate = this.audioContext.sampleRate;
      const numChannels = 1;
      const bitsPerSample = 16;

      // 计算总样本数
      const totalSamples = audioBuffer.length * audioBuffer[0].data.length;

      // 创建WAV文件头
      const buffer = new ArrayBuffer(44 + totalSamples * 2);
      const view = new DataView(buffer);

      // WAV文件头
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };

      writeString(0, 'RIFF');
      view.setUint32(4, 36 + totalSamples * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
      view.setUint16(32, numChannels * bitsPerSample / 8, true);
      view.setUint16(34, bitsPerSample, true);
      writeString(36, 'data');
      view.setUint32(40, totalSamples * 2, true);

      // 写入音频数据
      let offset = 44;
      for (const frame of audioBuffer) {
        for (let i = 0; i < frame.data.length; i++) {
          // 将Uint8转换为Int16
          const sample = (frame.data[i] - 128) * 256;
          view.setInt16(offset, sample, true);
          offset += 2;
        }
      }

      return new Blob([buffer], { type: 'audio/wav' });

    } catch (error) {
      console.error('ChronoTranslate: Error converting audio buffer to blob:', error);
      return null;
    }
  }
}

// 加载调试工具
const debugScript = document.createElement('script');
debugScript.src = chrome.runtime.getURL('debug.js');
document.head.appendChild(debugScript);

// 初始化插件
const chronoTranslate = new ChronoTranslate();

// 启用调试日志
if (window.ChronoTranslateDebugger) {
  window.ChronoTranslateDebugger.startPerformanceMonitoring();
}
