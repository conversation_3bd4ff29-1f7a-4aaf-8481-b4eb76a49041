/* ChronoTranslate 样式文件 */

.chrono-translate-panel {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin: 16px 0;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  box-sizing: border-box;
}

.ct-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.ct-logo {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ct-status {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
}

.ct-status-normal {
  background-color: #f5f5f5;
  color: #666;
}

.ct-status-loading {
  background-color: #fff3e0;
  color: #f57c00;
}

.ct-status-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.ct-status-error {
  background-color: #ffebee;
  color: #c62828;
}

.ct-status-stopped {
  background-color: #fafafa;
  color: #757575;
}

.ct-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.ct-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.ct-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ct-btn:active {
  transform: translateY(0);
}

.ct-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.ct-btn-primary {
  background-color: #1976d2;
  color: white;
}

.ct-btn-primary:hover:not(:disabled) {
  background-color: #1565c0;
}

.ct-btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
}

.ct-btn-secondary:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.ct-select {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  min-width: 120px;
}

.ct-select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.ct-mode-info {
  font-size: 12px;
  color: #666;
  font-style: italic;
  flex: 1;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chrono-translate-panel {
    margin: 12px 0;
    padding: 12px;
  }
  
  .ct-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .ct-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .ct-btn {
    min-width: auto;
  }
  
  .ct-mode-info {
    text-align: left;
    margin-top: 4px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .chrono-translate-panel {
    background: #1e1e1e;
    border-color: #333;
    color: #e0e0e0;
  }
  
  .ct-header {
    border-bottom-color: #333;
  }
  
  .ct-status-normal {
    background-color: #333;
    color: #ccc;
  }
  
  .ct-btn-secondary {
    background-color: #333;
    color: #e0e0e0;
    border-color: #555;
  }
  
  .ct-btn-secondary:hover:not(:disabled) {
    background-color: #444;
  }
  
  .ct-select {
    background-color: #333;
    border-color: #555;
    color: #e0e0e0;
  }
  
  .ct-mode-info {
    color: #aaa;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chrono-translate-panel {
  animation: fadeIn 0.3s ease-out;
}

/* 加载动画 */
.ct-status-loading::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  border: 2px solid #f57c00;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 进度条动画 */
.ct-progress {
  width: 100%;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 8px;
}

.ct-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.ct-progress-bar.indeterminate {
  width: 30% !important;
  animation: indeterminate 2s infinite linear;
}

@keyframes indeterminate {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* 音频可视化 */
.ct-audio-visualizer {
  display: flex;
  align-items: center;
  gap: 2px;
  height: 20px;
  margin-left: 8px;
}

.ct-audio-bar {
  width: 3px;
  background-color: #1976d2;
  border-radius: 1px;
  animation: audioWave 1.5s ease-in-out infinite;
}

.ct-audio-bar:nth-child(1) { animation-delay: 0s; }
.ct-audio-bar:nth-child(2) { animation-delay: 0.1s; }
.ct-audio-bar:nth-child(3) { animation-delay: 0.2s; }
.ct-audio-bar:nth-child(4) { animation-delay: 0.3s; }
.ct-audio-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes audioWave {
  0%, 100% {
    height: 4px;
    opacity: 0.3;
  }
  50% {
    height: 16px;
    opacity: 1;
  }
}

/* 通知样式 */
.ct-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  max-width: 300px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.ct-notification.show {
  transform: translateX(0);
}

.ct-notification.success {
  border-left: 4px solid #4caf50;
}

.ct-notification.error {
  border-left: 4px solid #f44336;
}

.ct-notification.warning {
  border-left: 4px solid #ff9800;
}

/* 工具提示 */
.ct-tooltip {
  position: relative;
  display: inline-block;
}

.ct-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.ct-tooltip:hover::after {
  opacity: 1;
}

/* 统计信息 */
.ct-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.ct-stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.ct-stat-value {
  font-weight: 500;
  color: #1976d2;
}

/* 高级控制 */
.ct-advanced {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.ct-advanced-toggle {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
}

.ct-advanced-content {
  margin-top: 8px;
  display: none;
}

.ct-advanced-content.show {
  display: block;
}

.ct-slider {
  width: 100%;
  margin: 8px 0;
}

.ct-slider input[type="range"] {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
}

.ct-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1976d2;
  cursor: pointer;
}

.ct-slider input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1976d2;
  cursor: pointer;
  border: none;
}
