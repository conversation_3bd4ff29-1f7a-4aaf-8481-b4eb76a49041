// options.js - 设置页面逻辑
class OptionsManager {
  constructor() {
    this.apiKeys = {};
    this.preferences = {};
    this.init();
  }

  init() {
    // 加载保存的设置
    this.loadSettings();
    
    // 绑定事件
    this.bindEvents();
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get(['apiKeys', 'preferences']);
      
      this.apiKeys = result.apiKeys || {};
      this.preferences = result.preferences || {
        defaultLanguage: 'zh-CN',
        voicePreference: 'neutral'
      };
      
      // 填充表单
      this.populateForm();
      
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showMessage('加载设置失败', 'error');
    }
  }

  populateForm() {
    // 填充API密钥（显示为星号）
    const googleTranslateKey = document.getElementById('googleTranslateKey');
    const googleTTSKey = document.getElementById('googleTTSKey');
    const openaiKey = document.getElementById('openaiKey');
    
    if (this.apiKeys.googleTranslate) {
      googleTranslateKey.value = '••••••••••••••••';
      googleTranslateKey.dataset.hasValue = 'true';
      this.updateApiStatus('google-translate-status', true);
    }
    
    if (this.apiKeys.googleTTS) {
      googleTTSKey.value = '••••••••••••••••';
      googleTTSKey.dataset.hasValue = 'true';
      this.updateApiStatus('google-tts-status', true);
    }
    
    if (this.apiKeys.openai) {
      openaiKey.value = '••••••••••••••••';
      openaiKey.dataset.hasValue = 'true';
      this.updateApiStatus('openai-status', true);
    }
    
    // 填充偏好设置
    document.getElementById('defaultLanguage').value = this.preferences.defaultLanguage;
    document.getElementById('voicePreference').value = this.preferences.voicePreference;
  }

  bindEvents() {
    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', () => {
      this.saveSettings();
    });
    
    // 测试按钮
    document.getElementById('testBtn').addEventListener('click', () => {
      this.testConnection();
    });
    
    // API密钥输入框事件
    const apiInputs = ['googleTranslateKey', 'googleTTSKey', 'openaiKey'];
    apiInputs.forEach(inputId => {
      const input = document.getElementById(inputId);
      
      input.addEventListener('focus', () => {
        if (input.dataset.hasValue === 'true') {
          input.value = '';
          input.dataset.hasValue = 'false';
        }
      });
      
      input.addEventListener('input', () => {
        if (input.value.length > 0) {
          input.dataset.hasValue = 'true';
        }
      });
    });
  }

  async saveSettings() {
    try {
      const saveBtn = document.getElementById('saveBtn');
      saveBtn.textContent = '保存中...';
      saveBtn.disabled = true;
      
      // 收集API密钥
      const newApiKeys = {...this.apiKeys};
      
      const googleTranslateKey = document.getElementById('googleTranslateKey');
      const googleTTSKey = document.getElementById('googleTTSKey');
      const openaiKey = document.getElementById('openaiKey');
      
      if (googleTranslateKey.value && googleTranslateKey.value !== '••••••••••••••••') {
        newApiKeys.googleTranslate = googleTranslateKey.value.trim();
      }
      
      if (googleTTSKey.value && googleTTSKey.value !== '••••••••••••••••') {
        newApiKeys.googleTTS = googleTTSKey.value.trim();
      }
      
      if (openaiKey.value && openaiKey.value !== '••••••••••••••••') {
        newApiKeys.openai = openaiKey.value.trim();
      }
      
      // 收集偏好设置
      const newPreferences = {
        defaultLanguage: document.getElementById('defaultLanguage').value,
        voicePreference: document.getElementById('voicePreference').value
      };
      
      // 保存到存储
      await chrome.storage.local.set({
        apiKeys: newApiKeys,
        preferences: newPreferences
      });
      
      // 通知background script更新API密钥
      chrome.runtime.sendMessage({
        action: 'updateApiKeys',
        data: newApiKeys
      });
      
      this.apiKeys = newApiKeys;
      this.preferences = newPreferences;
      
      // 更新API状态
      this.updateApiStatus('google-translate-status', !!newApiKeys.googleTranslate);
      this.updateApiStatus('google-tts-status', !!newApiKeys.googleTTS);
      this.updateApiStatus('openai-status', !!newApiKeys.openai);
      
      // 重新填充表单（显示星号）
      this.populateForm();
      
      this.showMessage('设置保存成功！', 'success');
      
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showMessage('保存设置失败：' + error.message, 'error');
    } finally {
      const saveBtn = document.getElementById('saveBtn');
      saveBtn.textContent = '保存设置';
      saveBtn.disabled = false;
    }
  }

  async testConnection() {
    const testBtn = document.getElementById('testBtn');
    testBtn.textContent = '测试中...';
    testBtn.disabled = true;
    
    try {
      // 测试翻译API
      if (this.apiKeys.googleTranslate) {
        await this.testGoogleTranslate();
        this.showMessage('Google Translate API 连接正常', 'success');
      } else if (this.apiKeys.openai) {
        await this.testOpenAI();
        this.showMessage('OpenAI API 连接正常', 'success');
      } else {
        this.showMessage('请先配置至少一个API密钥', 'error');
      }
      
    } catch (error) {
      console.error('API test failed:', error);
      this.showMessage('API连接测试失败：' + error.message, 'error');
    } finally {
      testBtn.textContent = '测试连接';
      testBtn.disabled = false;
    }
  }

  async testGoogleTranslate() {
    const apiKey = this.apiKeys.googleTranslate;
    const url = `https://translation.googleapis.com/language/translate/v2?key=${apiKey}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: ['Hello'],
        target: 'zh-CN',
        format: 'text'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.data || !data.data.translations) {
      throw new Error('Invalid API response');
    }
  }

  async testOpenAI() {
    const apiKey = this.apiKeys.openai;
    
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Invalid API response');
    }
  }

  updateApiStatus(statusId, isConfigured) {
    const statusElement = document.getElementById(statusId);
    if (statusElement) {
      if (isConfigured) {
        statusElement.textContent = '已配置';
        statusElement.className = 'api-status configured';
      } else {
        statusElement.textContent = '未配置';
        statusElement.className = 'api-status not-configured';
      }
    }
  }

  showMessage(message, type) {
    const messageElement = document.getElementById('statusMessage');
    messageElement.textContent = message;
    messageElement.className = `status-message status-${type}`;
    messageElement.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
      messageElement.style.display = 'none';
    }, 3000);
  }
}

// 初始化设置管理器
document.addEventListener('DOMContentLoaded', () => {
  new OptionsManager();
});
