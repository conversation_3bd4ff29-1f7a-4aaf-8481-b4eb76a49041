// debug.js - 调试工具
class ChronoTranslateDebugger {
  constructor() {
    this.logs = [];
    this.maxLogs = 100;
    this.isEnabled = false;
    
    this.init();
  }

  init() {
    // 检查是否启用调试模式
    chrome.storage.local.get(['debugMode'], (result) => {
      this.isEnabled = result.debugMode || false;
      if (this.isEnabled) {
        this.createDebugPanel();
      }
    });
  }

  log(level, message, data = null) {
    if (!this.isEnabled) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level,
      message: message,
      data: data
    };

    this.logs.push(logEntry);
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // 输出到控制台
    const consoleMethod = level === 'error' ? 'error' : 
                         level === 'warn' ? 'warn' : 'log';
    console[consoleMethod](`[ChronoTranslate ${level.toUpperCase()}]`, message, data);

    // 更新调试面板
    this.updateDebugPanel();
  }

  createDebugPanel() {
    // 避免重复创建
    if (document.getElementById('ct-debug-panel')) return;

    const panel = document.createElement('div');
    panel.id = 'ct-debug-panel';
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      width: 400px;
      height: 300px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      font-family: monospace;
      font-size: 12px;
      border-radius: 8px;
      z-index: 10000;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    `;

    panel.innerHTML = `
      <div style="padding: 10px; background: rgba(255, 255, 255, 0.1); display: flex; justify-content: space-between; align-items: center;">
        <span>ChronoTranslate Debug</span>
        <div>
          <button id="ct-debug-clear" style="margin-right: 5px; padding: 2px 6px; font-size: 10px;">Clear</button>
          <button id="ct-debug-export" style="margin-right: 5px; padding: 2px 6px; font-size: 10px;">Export</button>
          <button id="ct-debug-close" style="padding: 2px 6px; font-size: 10px;">×</button>
        </div>
      </div>
      <div id="ct-debug-content" style="flex: 1; overflow-y: auto; padding: 10px;"></div>
    `;

    document.body.appendChild(panel);

    // 绑定事件
    document.getElementById('ct-debug-clear').addEventListener('click', () => {
      this.logs = [];
      this.updateDebugPanel();
    });

    document.getElementById('ct-debug-export').addEventListener('click', () => {
      this.exportLogs();
    });

    document.getElementById('ct-debug-close').addEventListener('click', () => {
      panel.remove();
    });

    // 使面板可拖拽
    this.makeDraggable(panel);
  }

  updateDebugPanel() {
    const content = document.getElementById('ct-debug-content');
    if (!content) return;

    content.innerHTML = this.logs.map(log => {
      const color = log.level === 'error' ? '#ff6b6b' :
                   log.level === 'warn' ? '#ffd93d' :
                   log.level === 'info' ? '#6bcf7f' : '#ffffff';
      
      return `
        <div style="margin-bottom: 5px; color: ${color};">
          <span style="opacity: 0.7;">[${log.timestamp.split('T')[1].split('.')[0]}]</span>
          <span style="font-weight: bold;">[${log.level.toUpperCase()}]</span>
          ${log.message}
          ${log.data ? `<pre style="margin: 2px 0; font-size: 10px; opacity: 0.8;">${JSON.stringify(log.data, null, 2)}</pre>` : ''}
        </div>
      `;
    }).join('');

    // 自动滚动到底部
    content.scrollTop = content.scrollHeight;
  }

  makeDraggable(element) {
    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    const header = element.querySelector('div');
    
    header.addEventListener('mousedown', (e) => {
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      if (e.target === header || header.contains(e.target)) {
        isDragging = true;
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;

        xOffset = currentX;
        yOffset = currentY;

        element.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
      }
    });

    document.addEventListener('mouseup', () => {
      initialX = currentX;
      initialY = currentY;
      isDragging = false;
    });
  }

  exportLogs() {
    const data = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.logs
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `chronotranslate-debug-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
  }

  // 性能监控
  startPerformanceMonitoring() {
    if (!this.isEnabled) return;

    // 监控内存使用
    if (performance.memory) {
      setInterval(() => {
        const memory = performance.memory;
        this.log('info', 'Memory Usage', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
      }, 30000); // 每30秒记录一次
    }

    // 监控API调用
    this.monitorAPIRequests();
  }

  monitorAPIRequests() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0];
      
      try {
        const response = await originalFetch(...args);
        const endTime = performance.now();
        
        if (url.includes('googleapis.com') || url.includes('openai.com')) {
          this.log('info', 'API Request', {
            url: url,
            status: response.status,
            duration: Math.round(endTime - startTime) + 'ms'
          });
        }
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        this.log('error', 'API Request Failed', {
          url: url,
          error: error.message,
          duration: Math.round(endTime - startTime) + 'ms'
        });
        throw error;
      }
    };
  }

  // 测试工具
  runTests() {
    this.log('info', 'Starting ChronoTranslate Tests');
    
    // 测试1: 检查必要元素
    const video = document.querySelector('video');
    this.log(video ? 'info' : 'error', 'Video Element', { found: !!video });
    
    // 测试2: 检查字幕
    if (video && video.textTracks) {
      const hasSubtitles = Array.from(video.textTracks).some(track => 
        track.kind === 'subtitles' || track.kind === 'captions'
      );
      this.log('info', 'Subtitles Available', { hasSubtitles });
    }
    
    // 测试3: 检查API密钥
    chrome.storage.local.get(['apiKeys'], (result) => {
      const apiKeys = result.apiKeys || {};
      this.log('info', 'API Keys Status', {
        googleTranslate: !!apiKeys.googleTranslate,
        googleTTS: !!apiKeys.googleTTS,
        openai: !!apiKeys.openai
      });
    });
    
    this.log('info', 'Tests Completed');
  }
}

// 全局调试器实例
window.ChronoTranslateDebugger = new ChronoTranslateDebugger();

// 开发者工具快捷方式
window.ctDebug = {
  enable: () => {
    chrome.storage.local.set({ debugMode: true });
    window.ChronoTranslateDebugger.isEnabled = true;
    window.ChronoTranslateDebugger.createDebugPanel();
  },
  disable: () => {
    chrome.storage.local.set({ debugMode: false });
    window.ChronoTranslateDebugger.isEnabled = false;
    const panel = document.getElementById('ct-debug-panel');
    if (panel) panel.remove();
  },
  test: () => window.ChronoTranslateDebugger.runTests(),
  export: () => window.ChronoTranslateDebugger.exportLogs()
};
