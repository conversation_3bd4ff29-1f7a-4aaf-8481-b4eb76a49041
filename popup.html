<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #1976d2;
      margin-bottom: 5px;
    }
    .subtitle {
      font-size: 12px;
      color: #666;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
      font-weight: 500;
    }
    .status.active {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    .status.inactive {
      background-color: #fff3e0;
      color: #f57c00;
    }
    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .btn {
      padding: 10px 15px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    .btn-primary {
      background-color: #1976d2;
      color: white;
    }
    .btn-primary:hover {
      background-color: #1565c0;
    }
    .btn-secondary {
      background-color: #f5f5f5;
      color: #333;
    }
    .btn-secondary:hover {
      background-color: #e0e0e0;
    }
    .footer {
      margin-top: 20px;
      text-align: center;
      font-size: 12px;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">ChronoTranslate</div>
    <div class="subtitle">YouTube实时翻译</div>
  </div>
  
  <div id="status" class="status inactive">
    请在YouTube视频页面使用
  </div>
  
  <div class="quick-actions">
    <button id="openOptions" class="btn btn-primary">打开设置</button>
    <button id="toggleTranslation" class="btn btn-secondary">启动翻译</button>
  </div>
  
  <div class="footer">
    v1.0.0 | 需要配置API密钥
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
