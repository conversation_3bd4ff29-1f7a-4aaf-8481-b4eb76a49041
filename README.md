好的。作为项目的技术负责人，准备清晰的文档是项目成功的基石。我将为你创建两份关键文档：

1.  **产品需求文档 (PRD - Product Requirements Document):** 面向所有项目相关人员（产品、开发、测试、设计），定义产品的**“做什么”**和**“为什么做”**。
2.  **技术方案文档 (TDD - Technical Design Document):** 面向开发团队，详细说明产品的**“如何实现”**。

---

### **文档一：产品需求文档 (PRD)**

**文档版本:** 1.0
**创建日期:** 2023年10月27日
**创建人:** Roo (Tech Lead)
**项目名称:** ChronoTranslate - YouTube 实时翻译插件

---

#### **1. 产品概述与愿景**

**1.1. 项目背景**
YouTube 是全球最大的视频平台，拥有海量的知识、教程和娱乐内容。然而，语言障碍是阻碍用户获取这些信息的主要壁垒。虽然 YouTube 自带的机翻字幕有所帮助，但“阅读”字幕会分散用户对视频画面的注意力，体验远不如直接“收听”母语内容。

**1.2. 产品愿景**
**ChronoTranslate** 旨在打破语言壁垒，通过先进的 AI 技术，将 YouTube 视频实时翻译成用户的母语语音进行播放，为全球用户提供无缝、沉浸式的跨语言视频观看体验。

**1.3. 目标用户画像**
*   **学生/研究人员 (小明):** 需要观看大量国外大学的公开课、学术讲座来获取前沿知识，但听力跟不上语速，阅读字幕效率低。
*   **职场人士 (李华):** 需要关注海外行业动态、学习国外软件教程，希望在工作之余能像听播客一样“收听”这些视频内容。
*   **兴趣爱好者 (王刚):** 喜欢追国外的科技评测、生活方式 Vlogger，希望第一时间以最自然的方式理解视频内容。

---

#### **2. 产品目标 (Objectives)**

*   **P0 (核心目标):** 实现对 YouTube 视频语音的实时、准确翻译，并以自然流畅的目标语言语音播放。
*   **P1 (体验目标):** 最小化翻译延迟，保证音画基本同步，不影响用户的沉浸感。
*   **P2 (可用性目标):** 提供简洁直观的操作界面，用户可以一键启动翻译并轻松切换语言。
*   **P3 (扩展性目标):** 允许用户配置自己的 AI 服务，以满足不同成本和质量的需求。

---

#### **3. 功能需求 (Features & User Stories)**

**Epic 1: 核心翻译功能**

| 用户故事 ID | 作为一名... | 我想要... | 以便我能... | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| US-101 | 用户 | 在 YouTube 播放页面一键启动或关闭翻译功能 | 自由控制是否需要翻译 | P0 |
| US-102 | 用户 | 选择我希望听到的目标翻译语言 | 将视频翻译成我的母语或我正在学习的语言 | P0 |
| US-103 | 系统 | 优先使用视频自带的精确字幕进行翻译和语音合成 | 获得延迟最低、成本最低、最准确的翻译体验 | P0 |
| US-104 | 系统 | 在视频没有可用字幕时，自动切换到捕获音频进行翻译的模式 | 即使视频没有字幕也能使用翻译功能 | P1 |
| US-105 | 用户 | 在翻译播放时，原始视频的声音被自动静音 | 避免两种语言混杂，保证听觉清晰 | P0 |

**Epic 2: 用户配置与管理**

| 用户故事 ID | 作为一名... | 我想要... | 以便我能... | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| US-201 | 高级用户 | 在插件的设置页面输入并保存我自己的云服务 API Key | 使用自己的账户来控制成本和用量 | P2 |
| US-202 | 用户 | 在设置中选择我偏好的合成语音音色（如男声/女声） | 获得更个性化的收听体验 | P2 |

**Epic 3: 用户界面 (UI) 与用户体验 (UX)**

| 用户故事 ID | 作为一名... | 我想要... | 以便我能... | 优先级 |
| :--- | :--- | :--- | :--- | :--- |
| US-301 | 用户 | 在 YouTube 视频播放器下方看到一个清晰的控制面板 | 方便地进行所有相关操作 | P1 |
| US-302 | 用户 | 在控制面板上看到当前的翻译状态（如：正在翻译/已停止/错误） | 了解插件的当前工作情况 | P1 |

---

#### **4. 非功能性需求**

*   **性能:**
    *   **字幕模式延迟:** 从原句出现到翻译语音播放，延迟应控制在 2 秒以内。
    *   **音频流模式延迟:** 延迟应力求控制在 5 秒以内。
    *   **资源占用:** 插件在后台工作时，CPU 占用率应尽可能低，不应导致浏览器明显卡顿。
*   **安全性:** 用户 API Key 必须在本地安全存储，绝不能在网络传输中明文暴露或硬编码在代码中。
*   **兼容性:** 兼容最新版本的 Google Chrome 浏览器。

#### **5. 待办/未来范围 (Out of Scope for V1.0)**

*   移动端浏览器支持。
*   视频下载与离线翻译。
*   高级语音克隆功能。
*   支持除 YouTube 之外的其他视频平台。

---

### **文档二：技术方案文档 (TDD)**

**文档版本:** 1.0
**创建日期:** 2023年10月27日
**创建人:** Roo (Tech Lead)
**关联 PRD:** ChronoTranslate PRD V1.0

---

#### **1. 系统架构**

本插件采用**客户端-云服务分离架构**。Chrome 插件作为客户端的**智能调度器**，负责 UI 交互、任务流编排和音视频同步。所有 AI 计算（ASR, NMT, TTS）均通过 API 调用外部云服务完成。

**架构图:**
```
+--------------------------+        +---------------------------------+
|   Chrome 插件 (客户端)   |        |      云 AI 服务 (Backend)       |
|--------------------------|        |---------------------------------|
| [ Content Script ]       |<------>| [ Background Script (调度器) ]  |
|  - UI 注入与控制         |        |  - API 代理与通信               |
|  - 字幕抓取              |        |  - 翻译工作流管理               |
|  - 音频同步播放          |        |  - 音频流捕获 (模式B)           |
|  - 与 Background 通信    |        +----------------|----------------+
+--------------------------+                         | (HTTPS API Calls)
                                                     v
                                       +-----------------------------+
                                       | ASR / NMT / TTS API         |
                                       | (Google, OpenAI, Azure 等)  |
                                       +-----------------------------+
```

#### **2. 模块设计与技术选型**

*   **Manifest V3:** 采用 Service Worker (`background.js`) 替代持久背景页，提高性能和安全性。
*   **Content Script (`content.js`):**
    *   **技术:** Vanilla JavaScript, DOM API。
    *   **职责:** 负责所有与 YouTube 页面的直接交互。注入 React 或 Vue 等重型框架会增加复杂性和性能开销，故不采用。
*   **Background Script (`background.js`):**
    *   **技术:** JavaScript, Fetch API, `chrome.runtime`, `chrome.storage`。
    *   **职责:** 插件核心逻辑。处理所有异步任务和外部 API 请求。作为 Service Worker，它按需运行，节省资源。
*   **音频处理与捕获:**
    *   **技术:** `chrome.tabCapture`, `Web Audio API`。
    *   **职责:** 在模式 B（无字幕时）下，`background.js` 使用 `tabCapture` 获取标签页音频流，并通过 `Web Audio API` 进行分块处理。
*   **配置存储:**
    *   **技术:** `chrome.storage.local` API。
    *   **职责:** 在 `options.js` 中调用，将用户的 API Key 和偏好设置安全地存储在本地。不使用 `sync` 是因为 API Key 属于敏感信息，不宜跨设备同步。

#### **3. 核心工作流实现**

**3.1. 模式 A: 字幕优先工作流 (低延迟，默认模式)**

1.  **[Content] 触发:** 用户点击“开始翻译”。
2.  **[Content] 字幕探测:** 检测 `document.querySelector('video').textTracks` 是否存在可用字幕。
3.  **[Content] 数据提取:** 若存在，则遍历 `track.cues`，提取所有字幕的 `text`, `startTime`, `endTime`，组成 `Task[]` 数组。
4.  **[Content -> Background] 任务派发:** 通过 `chrome.runtime.sendMessage` 将 `Task[]` 发送给 `background.js`。
5.  **[Background] 批量处理:**
    *   将 `Task[]` 中的文本分批（减少 API 请求次数）发送给 **NMT API** (e.g., Google Translate)。
    *   收到翻译结果后，**并行地**为每一句翻译后的文本请求 **TTS API** (e.g., Google TTS)，获取音频数据。
    *   将音频数据转为 `Blob`，通过 `URL.createObjectURL()` 生成可播放的 `blob:` URL。
6.  **[Background -> Content] 结果返回:** 将包含 `startTime`, `endTime`, 和 `audioUrl` 的 `Result[]` 数组返回给 `content.js`。
7.  **[Content] 音频同步:**
    *   静音原始视频 `video.muted = true`。
    *   监听 `video.ontimeupdate` 事件。
    *   在回调中，根据 `video.currentTime` 在 `Result[]` 中查找匹配的音频片段，并使用一个 `<audio>` 元素进行播放。

**3.2. 模式 B: 音频流工作流 (高延迟，后备模式)**

1.  **[Content] 触发:** 用户点击“开始翻译”，但未检测到字幕。
2.  **[Content -> Background] 请求捕获:** 发送消息请求 `background.js` 启动音频捕获。
3.  **[Background] 音频捕获:**
    *   调用 `chrome.tabCapture.capture()` 获取音频流 `MediaStream`。
    *   使用 `Web Audio API` 的 `AudioContext` 和 `ScriptProcessorNode` 将流切割成例如 5 秒的音频块 (PCM data)。
4.  **[Background] 实时处理管道:**
    *   将音频块编码为 FLAC 或 WAV 格式。
    *   发送至 **ASR API** (e.g., OpenAI Whisper) 进行语音识别，获取带时间戳的文本。
    *   立即将识别出的文本送入 **NMT -> TTS** 流程（同模式 A）。
    *   **这是一个持续的流式处理过程。**
5.  **[Background -> Content] 实时推送:** 每当一个音频片段的 `audioUrl` 生成后，立即将其与 `startTime` 推送给 `content.js`。
6.  **[Content] 音频同步:** 与模式 A 的步骤 7 类似，但播放队列是动态增加的。

#### **4. 风险与缓解方案**

*   **风险 1: 音画不同步**
    *   **缓解:**
        1.  以视频的 `timeupdate` 事件为唯一时间基准。
        2.  实现一个小的播放缓冲区，微调播放时机。
        3.  在模式 A 中进行预取（Prefetch），提前合成未来几十秒的语音。
*   **风险 2: API 成本过高**
    *   **缓解:**
        1.  强制用户提供自己的 API Key，将成本透明化。
        2.  使用 `IndexedDB` 对已翻译的视频（以视频 ID 为 key）进行结果缓存，避免重复请求。
*   **风险 3: YouTube 页面改版导致插件失效**
    *   **缓解:**
        1.  使用更稳定的选择器（如 ARIA roles, `data-*` 属性）而非易变的 class 名称。
        2.  考虑将核心选择器配置放在一个可远程更新的 JSON 文件中，实现热修复。

#### **5. 开发里程碑 (Roadmap)**

*   **Phase 1 (MVP):**
    *   完成模式 A（字幕优先）的端到端功能。
    *   集成 Google Translate 和 Google TTS API。
    *   完成基本的 UI 和 API Key 配置页面。
*   **Phase 2:**
    *   实现模式 B（音频流）的核心功能。
    *   集成 OpenAI Whisper 作为 ASR 选项。
    *   引入基于 `IndexedDB` 的持久化缓存。
*   **Phase 3:**
    *   优化同步算法，提升体验。
    *   增加对更多翻译/TTS 服务的支持（如 DeepL, Azure）。
    *   代码重构与性能调优。