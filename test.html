<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ChronoTranslate 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #e8f5e8; color: #2e7d32; }
        .error { background: #ffebee; color: #c62828; }
        .warning { background: #fff3e0; color: #f57c00; }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1565c0; }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🌐 ChronoTranslate 测试页面</h1>
    
    <div class="test-section">
        <h2>插件状态检查</h2>
        <button onclick="checkExtension()">检查插件状态</button>
        <div id="extension-status"></div>
    </div>

    <div class="test-section">
        <h2>API配置测试</h2>
        <button onclick="testAPIs()">测试API连接</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h2>功能测试</h2>
        <button onclick="testTranslation()">测试翻译功能</button>
        <button onclick="testTTS()">测试语音合成</button>
        <button onclick="testSTT()">测试语音识别</button>
        <div id="function-status"></div>
    </div>

    <div class="test-section">
        <h2>调试工具</h2>
        <button onclick="enableDebug()">启用调试模式</button>
        <button onclick="disableDebug()">禁用调试模式</button>
        <button onclick="runDebugTests()">运行调试测试</button>
        <button onclick="exportDebugLogs()">导出调试日志</button>
    </div>

    <div class="test-section">
        <h2>使用说明</h2>
        <ol>
            <li>确保已安装ChronoTranslate插件</li>
            <li>在插件设置中配置API密钥</li>
            <li>打开YouTube视频页面进行测试</li>
            <li>使用此页面进行功能验证</li>
        </ol>
        
        <h3>测试用YouTube视频推荐</h3>
        <ul>
            <li><a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank">带英文字幕的视频</a></li>
            <li><a href="https://www.youtube.com/watch?v=jNQXAC9IVRw" target="_blank">TED演讲（多语言字幕）</a></li>
        </ul>
    </div>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function checkExtension() {
            clearResults('extension-status');
            
            try {
                // 检查Chrome扩展API
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    showResult('extension-status', '✓ Chrome扩展API可用');
                    
                    // 检查插件是否安装
                    chrome.runtime.sendMessage('your-extension-id', {action: 'ping'}, (response) => {
                        if (chrome.runtime.lastError) {
                            showResult('extension-status', '⚠ 插件未安装或未启用', 'warning');
                        } else {
                            showResult('extension-status', '✓ ChronoTranslate插件已安装');
                        }
                    });
                } else {
                    showResult('extension-status', '✗ Chrome扩展API不可用', 'error');
                }
            } catch (error) {
                showResult('extension-status', `✗ 检查失败: ${error.message}`, 'error');
            }
        }

        async function testAPIs() {
            clearResults('api-status');
            showResult('api-status', '正在测试API连接...', 'warning');
            
            // 这里需要实际的API测试逻辑
            // 由于安全限制，实际测试需要在插件环境中进行
            setTimeout(() => {
                showResult('api-status', '⚠ API测试需要在插件设置页面进行', 'warning');
                showResult('api-status', '请打开插件设置页面点击"测试连接"按钮');
            }, 1000);
        }

        async function testTranslation() {
            clearResults('function-status');
            showResult('function-status', '翻译功能测试需要在YouTube页面进行', 'warning');
            showResult('function-status', '请打开YouTube视频页面，启动翻译功能进行测试');
        }

        async function testTTS() {
            clearResults('function-status');
            showResult('function-status', '语音合成测试需要配置API密钥', 'warning');
        }

        async function testSTT() {
            clearResults('function-status');
            showResult('function-status', '语音识别测试需要在音频模式下进行', 'warning');
        }

        function enableDebug() {
            if (typeof ctDebug !== 'undefined') {
                ctDebug.enable();
                showResult('function-status', '✓ 调试模式已启用');
            } else {
                showResult('function-status', '⚠ 请在YouTube页面启用调试模式', 'warning');
                showResult('function-status', '在控制台输入: ctDebug.enable()');
            }
        }

        function disableDebug() {
            if (typeof ctDebug !== 'undefined') {
                ctDebug.disable();
                showResult('function-status', '✓ 调试模式已禁用');
            } else {
                showResult('function-status', '⚠ 请在YouTube页面禁用调试模式', 'warning');
            }
        }

        function runDebugTests() {
            if (typeof ctDebug !== 'undefined') {
                ctDebug.test();
                showResult('function-status', '✓ 调试测试已运行，查看调试面板');
            } else {
                showResult('function-status', '⚠ 请先在YouTube页面启用调试模式', 'warning');
            }
        }

        function exportDebugLogs() {
            if (typeof ctDebug !== 'undefined') {
                ctDebug.export();
                showResult('function-status', '✓ 调试日志已导出');
            } else {
                showResult('function-status', '⚠ 请先在YouTube页面启用调试模式', 'warning');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkExtension, 1000);
        });
    </script>
</body>
</html>
